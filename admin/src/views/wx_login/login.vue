<script lang="ts" setup>
import {
    // createapp,
    createCode,
    codeLogin } from '@/api/wx_login'

const visible = defineModel('visible')
const showCode = ref(false)
// 定时器
let timer = null
// 轮询接口请求中
let isPolling = false
const codeDta = ref({
    url: '',
    wId: ''

})
// 是否创建二维码
let isCreateCode = false
const emit = defineEmits(['doen'])

const onAddPeopleClick = async () => {
    if(isCreateCode) return;
    isCreateCode = true
   const row = {'wcId': ''};

    isCreateCode = false
    showCode.value = true
    // 创建二维码
    const resCode = await createCode({ "wcId": row.wcId })
   console.log(resCode)
    codeDta.value = resCode
    codeDta.value.wId = resCode.wId
    if(!timer) {
        eachHttpCode();
        timer = setInterval(() => {
            eachHttpCode();
        },3000)
    }
}

// 轮询接口
const eachHttpCode = async () => {
    if(!isPolling) {
        isPolling = true
        try {
            const res = await codeLogin({
                wId: codeDta.value.wId,
            })
          console.log(res)
            ElMessage({
                message: '登录成功',
                type: 'success',
            })
            closeShowCode();
            visible.value = false
            emit('doen')
        }catch(err) {
            isPolling = false
        }
    }
}
const close = () => {
    visible.value = false
}

const closeShowCode = () => {
    showCode.value = false
    clearInterval(timer)
    timer = null
    isCreateCode = false
}

</script>

<template>
    <div class="edit-popup">
        <el-dialog
            v-model="visible"
            :append-to-body="true"
            :width="520"
            @closed="close"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            title="新建数字人"
        >
            <div style="display: flex; align-items: center;" @click="onAddPeopleClick">
                <div style="width: 60px; height: 60px; background: #f1f1f1; display: flex; align-items: center; justify-content: center">
                    <icon name="el-icon-Plus" />
                </div>
                <span style="margin-left: 10px;">新建数字人</span>
            </div>
        </el-dialog>
        <el-dialog
            v-model="showCode"
            :append-to-body="true"
            :width="400"
            @closed="closeShowCode"
            title="二维码"
        >
            <img :src="codeDta?.url" style="width: 350px; height: 350px;" />
        </el-dialog>
    </div>
</template>

