<script lang="ts" setup name="consumerLists">
import {
    apiWxLoginDelete,
    apiWxLoginLists,
    codeLogin,
    createCode,
    exitLogin,
    isOnlineClick
    // getUserList,
    // getFriends
} from '@/api/wx_login.ts'
import { usePaging } from '@/hooks/usePaging'
import Login from './login.vue'

const showFriends = ref(false)
const friendsList = ref([])
const router = useRouter()

const showEdit = ref(false)
const queryParams = reactive({
    nickName: '',
    bindMobile: ''
})
// 当前点击的数据
const currentItem = ref<any>()
// 显示扫描二维码
const showScan = ref(false)
// 定时器
let timer = null

// 轮询接口请求中
let isPolling = false
const friendsPager = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
})

const openWxPage = () => {
    router .push({ path: '/virtualPeople/wechat', query: { id: 666 } })
}
const getFriendsList = async () => {
    try {
        const res = await getFriends({
            wxid: currentItem.value.userName,
            page: friendsPager.currentPage,
            limit: friendsPager.pageSize
        })
        console.log(res.data)
        friendsList.value = res.data
        friendsPager.total = res.total
        console.log(friendsList)
    } catch (err) {
        console.error(err)
    }
}

const onShowFriendsClick = async (row: any) => {
    currentItem.value = row
    friendsPager.currentPage = 1
    await getFriendsList()
    showFriends.value = true
}
// const onShowFriendsClick = async (row: any) => {
//   try {
//     const res = await getFriends({ wxid: row.userName })
//     friendsList.value = res.data
//     showFriends.value = true
//   } catch (err) {
//     console.error(err)
//   }
// }

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiWxLoginLists,
    params: queryParams
})
onActivated(() => {
    getLists()
})



const onLoginClick = async (row: any) => {
    // 创建二维码
    console.log(row);
    const res = await createCode({ wcId: row.wcId })
    currentItem.value = res
    showScan.value = true
    if (!timer) {
        eachHttpCode()
        timer = setInterval(() => {
            eachHttpCode()
        }, 10000)
    }
}

// 轮询接口
const eachHttpCode = async () => {
    if (!isPolling) {
        isPolling = true
        try {
            await codeLogin({
                wId: currentItem.value.wId,
            })
            ElMessage({
                message: '登录成功',
                type: 'success'
            })
            closeShowCode()
            getLists()
        } catch (err) {
            isPolling = false
        }
    }
}

const handleAdd = async () => {
    showEdit.value = true
}

// 退出
const onExitClick = async (row: any) => {
    try {
        await exitLogin({ wcId: row.wcId })
        ElMessage({
            message: '退出成功',
            type: 'success'
        })
        getLists()
    } catch (err) {
        console.error(err)
    }
}

const closeShowCode = () => {
    showScan.value = false
    clearInterval(timer)
    timer = null
}

const onDeleteClick = async (row: any) => {
    try {
        await apiWxLoginDelete({  id: row.id })
        ElMessage({
            message: '删除成功',
            type: 'success'
        })
        getLists()
    } catch (err) {
        console.error(err)
    }
}
const onIsOnlineClick = async (row: any) => {
    try {
       const isOnline = await isOnlineClick({  wId: row.wId })
        console.log(isOnline.isOnline);
        ElMessage({
            message: isOnline.isOnline?'在线': '离线',
            type: isOnline.isOnline?'success':'error'
        })

    } catch (err) {
        console.error(err)
    }
}


getLists()
</script>

<template>
    <div>
        <el-card class="!border-none" shadow="never">
            <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
                <el-form-item label="微信昵称">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.nickName"
                        placeholder="请输入微信昵称"
                        clearable
                    />
                </el-form-item>
                <el-form-item label="手机号">
                    <el-input
                        class="w-[280px]"
                        v-model="queryParams.mobilePhone"
                        placeholder="请输入手机号"
                        clearable
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none mt-4" shadow="never">
            <div style="margin-bottom: 10px">
                <el-button type="primary" @click="handleAdd">
                    <icon name="el-icon-Plus" />
                    <span>新增数字人</span>
                </el-button>
            </div>
            <el-table size="large" v-loading="pager.loading" :data="pager.lists">
                <el-table-column label="头像" prop="headUrl" min-width="100">
                    <template #default="{ row }">
                        <el-avatar :size="50" :src="row.headUrl" />
                    </template>
                </el-table-column>
                <el-table-column label="微信昵称" prop="nickName" min-width="120" />
                <el-table-column label="手机号码" prop="mobilePhone" min-width="100" />
                <el-table-column label="微信号" prop="wcId" min-width="100" />

                <el-table-column label="登录状态" prop="bindMobile" min-width="100">
                    <template #default="{ row }">
                        <span v-if="row.status == 1" style="color: red">未登录</span>
                        <span v-if="row.status == 3" style="color: green">在线</span>
                        <span v-if="row.status == 2" style="color: darkgray">离线</span>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="create_time" min-width="100" />
                <el-table-column label="操作" width="350" fixed="right">
                    <template #default="{ row }">
                        <el-space>
                            <template v-if="row.status === 2 || row.status === 1">
                                <el-button @click="onLoginClick(row)" type="primary" size="small"
                                >登录</el-button>
                            </template>
                            <template v-if="row.status === 3">
                                <el-popconfirm title="确定要退出吗" @confirm="onExitClick(row)">
                                    <template #reference>
                                        <el-button type="danger" size="small">退出</el-button>
                                    </template>
                                </el-popconfirm>
                            </template>
                            <el-popconfirm title="确定要删除吗" @confirm="onDeleteClick(row)">
                                <template #reference>
                                    <el-button type="danger" size="small">删除</el-button>
                                </template>
                            </el-popconfirm>
                                <el-button @click="onIsOnlineClick(row)" type="primary" size="small"
                                >是否在线</el-button>

                        </el-space>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end mt-4">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <Login v-model:visible="showEdit" @doen="getLists()" />
        <el-dialog
            v-model="showScan"
            :append-to-body="true"
            :width="400"
            @closed="closeShowCode"
            title="二维码"
        >
            <img :src="currentItem?.url" style="width: 350px; height: 350px" />
        </el-dialog>

        <!-- 好友列表对话框 -->
        <el-dialog v-model="showFriends" title="好友列表" width="600px">
            <el-table :data="friendsList">
                <el-table-column label="头像">
                    <template #default="{ row }">
                        <el-avatar :size="50" :src="row.smallHeadImgUrl" />
                    </template>
                </el-table-column>
                <el-table-column label="类型">
                    <template #default="{ row }">
                        <span v-if="row.type === 1">好友</span>
                        <span v-else-if="row.type === 2">群</span>
                    </template>
                </el-table-column>
                <el-table-column prop="nickName" label="名称" />
                <el-table-column prop="labelList" label="标签" />
                <el-table-column prop="remark" label="备注" />
            </el-table>
            <div class="flex justify-end mt-4">
                <el-pagination
                    v-model:current-page="friendsPager.currentPage"
                    v-model:page-size="friendsPager.pageSize"
                    :total="friendsPager.total"
                    @current-change="getFriendsList"
                />
            </div>
        </el-dialog>

    </div>
</template>
