<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
                <el-form-item label="绑定手机号" prop="mobilePhone">
                    <el-input class="w-[280px]" v-model="queryParams.mobilePhone" clearable placeholder="请输入绑定手机号" />
                </el-form-item>
                <el-form-item label="微信id" prop="wcId">
                    <el-input class="w-[280px]" v-model="queryParams.wcId" clearable placeholder="请输入微信id" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">
            <el-button v-perms="['bot.wx_login/add']" type="primary" @click="handleAdd">
                <template #icon>
                    <icon name="el-icon-Plus" />
                </template>
                新增
            </el-button>
            <el-button
                v-perms="['bot.wx_login/delete']"
                :disabled="!selectData.length"
                @click="handleDelete(selectData)"
            >
                删除
            </el-button>
            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="设备ID" prop="wId" show-overflow-tooltip />
                    <el-table-column label="头像" prop="headUrl" show-overflow-tooltip />
                    <el-table-column label="昵称" prop="nickName" show-overflow-tooltip />
                    <el-table-column label="绑定手机号" prop="mobilePhone" show-overflow-tooltip />
                    <el-table-column label="状态" prop="status" show-overflow-tooltip />
                    <el-table-column label="微信id" prop="wcId" show-overflow-tooltip />
                    <el-table-column label="登录成功信息" prop="data" show-overflow-tooltip />

                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
                             <el-button
                                v-perms="['bot.wx_login/edit']"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                v-perms="['bot.wx_login/delete']"
                                type="danger"
                                link
                                @click="handleDelete(row.id)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <login-popup v-if="showEdit" ref="editRef" :dict-data="dictData" @success="getLists" @close="showEdit = false" />
    </div>
</template>

<script lang="ts" setup name="wxLoginLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictData } from '@/hooks/useDictOptions'
import { apiWxLoginLists, apiWxLoginDelete } from '@/api/wx_login'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import LoginPopup from './login.vue'

const editRef = shallowRef<InstanceType<typeof LoginPopup>>()
// 是否显示编辑框
const showEdit = ref(false)


// 查询条件
const queryParams = reactive({
    mobilePhone: '',
    wcId: '',
})

// 选中数据
const selectData = ref<any[]>([])

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}

// 获取字典数据
const { dictData } = useDictData('')

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiWxLoginLists,
    params: queryParams
})

// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiWxLoginDelete({ id })
    getLists()
}

getLists()
</script>

