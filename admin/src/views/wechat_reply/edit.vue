<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
              <el-form-item label="系统" prop="client">
                <el-input v-model="formData.client" disabled />
              </el-form-item>
                <el-form-item label="关键词" prop="keyword">
                    <el-input v-model="formData.keyword" clearable placeholder="请输入关键词" />
                </el-form-item>
                <el-form-item label="匹配方式" prop="matching_type">
                    <el-radio  v-model="formData.matching_type"  value="1">模糊匹配</el-radio>
                    <el-radio  v-model="formData.matching_type" value="2">完全匹配</el-radio>

                </el-form-item>
                <el-form-item label="内容类型" prop="content_type">
                    <el-radio  v-model="formData.content_type"  value="1">文本</el-radio>
                    <el-radio  v-model="formData.content_type" value="2">文档</el-radio>
                </el-form-item>
                <el-form-item label="回复内容" prop="content">
                    <el-input v-model="formData.content" clearable placeholder="请输入回复内容" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio  v-model="formData.status"  value="1" >启用</el-radio>
                    <el-radio  v-model="formData.status" value="2">禁用</el-radio>
                </el-form-item>


            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="wechatReplyEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiWechatReplyAdd, apiWechatReplyEdit, apiWechatReplyDetail } from '@/api/wechat_reply'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')


// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑自定义回复' : '新增自定义回复'
})

// 表单数据
const formData = reactive({
    id: '',
    name: '',
    client: '',
    keyword: '',
    reply_type: '',
    matching_type: '',
    content_type: '',
    content: '',
    status: '',
    sort: '',

})


// 表单验证
const formRules = reactive<any>({
    client: [{
        required: true,
        message: '请选择系统ID',
        trigger: ['blur']
    }],
    name: [{
        required: true,
        message: '请输入规则名称',
        trigger: ['blur']
    }],
    keyword: [{
        required: true,
        message: '请输入关键词',
        trigger: ['blur']
    }],
    reply_type: [{
        required: true,
        message: '请选择回复类型 1-关注回复 2-关键字回复 3-默认回复',
        trigger: ['blur']
    }],
    matching_type: [{
        required: true,
        message: '请选择匹配方式',
        trigger: ['blur']
    }],
    content_type: [{
        required: true,
        message: '请选择匹配方式',
        trigger: ['blur']
    }],
    content: [{
        required: true,
        message: '请输入回复内容',
        trigger: ['blur']
    }],
    status: [{
        required: true,
        message: '请输入启动状态：1-启动；0-关闭',
        trigger: ['blur']
    }],
    sort: [{
        required: true,
        message: '请输入排序',
        trigger: ['blur']
    }],
})


// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
    
    
}

const getDetail = async (row: Record<string, any>) => {
    const data = await apiWechatReplyDetail({
        id: row.id
    })
    setFormData(data)
}


// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData,  }
    mode.value == 'edit' 
        ? await apiWechatReplyEdit(data) 
        : await apiWechatReplyAdd(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}



defineExpose({
    open,
    setFormData,
    getDetail
})
</script>
