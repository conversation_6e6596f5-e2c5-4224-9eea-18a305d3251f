<template>
    <div>
        <el-card class="!border-none mb-4 top" shadow="never">
            <el-button  @click="handleBack" type="danger" plain>返回</el-button>
        </el-card>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
                <el-form-item label="关键词" prop="keyword">
                    <el-input class="w-[280px]" v-model="queryParams.keyword" clearable placeholder="请输入关键词" />
                </el-form-item>

                <el-form-item label="匹配方式" prop="matching_type">
                    <el-input class="w-[280px]" v-model="queryParams.matching_type" clearable placeholder="请输入匹配方式：1-全匹配；2-模糊匹配" />
                </el-form-item>
                <el-form-item label="内容类型" prop="content_type">
                    <el-input class="w-[280px]" v-model="queryParams.content_type" clearable placeholder="请输入内容类型：1-文本" />
                </el-form-item>

                <el-form-item label="状态" prop="status">
                    <el-input class="w-[280px]" v-model="queryParams.status" clearable placeholder="请输入启动状态：1-启动；0-关闭" />
                </el-form-item>


                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">
            <el-button v-perms="['work.wechat_reply/add']" type="primary" @click="handleAdd">
                <template #icon>
                    <icon name="el-icon-Plus" />
                </template>
                新增
            </el-button>
            <el-button
                v-perms="['work.wechat_reply/delete']"
                :disabled="!selectData.length"
                @click="handleDelete(selectData)"
            >
                删除
            </el-button>
            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />

                    <el-table-column label="关键词" prop="keyword" show-overflow-tooltip />
                    <el-table-column label="匹配方式" prop="matching_type" show-overflow-tooltip />
                    <el-table-column label="内容类型" prop="content_type" show-overflow-tooltip />
                    <el-table-column label="内容" prop="content_type" show-overflow-tooltip />
                    <el-table-column label="状态" prop="status" show-overflow-tooltip />


                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
                            <el-button
                                v-perms="['work.wechat_reply/edit']"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                v-perms="['work.wechat_reply/delete']"
                                type="danger"
                                link
                                @click="handleDelete(row.id)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <edit-popup v-if="showEdit" ref="editRef" :dict-data="dictData" @success="getLists" @close="showEdit = false" />
    </div>
</template>

<script lang="ts" setup name="wechatReplyLists">
import { usePaging } from '@/hooks/usePaging'

import { getDictData } from '@/api/app'
import { apiWechatReplyLists, apiWechatReplyDelete } from '@/api/wechat_reply'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'
import useMultipleTabs from '@/hooks/useMultipleTabs'
const { removeTab } = useMultipleTabs()
const route = useRoute()
import { useRouter } from 'vue-router'
import { apiAuthSystemXm } from '@/api/auth_system.ts'
const router = useRouter()

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
// 是否显示编辑框
const showEdit = ref(false)


// 查询条件
const queryParams = reactive({
    name: '',
    keyword: '',
    reply_type: '',
    matching_type: '',
    content_type: '',
    content: '',
    status: '',
    sort: '',
    client: route.query.client??''
})

// 选中数据
const selectData = ref<any[]>([])

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}
const status = ref()
const method= ref()
const type = ref()
const client = ref()

const getDict = async () => {
      status.value  = await getDictData({
        type: "unified_login_reply_status"
    })
    method.value  =  await getDictData({
        type: "unified_login_reply_method"
    })
     type.value =  await getDictData({
        type: "unified_login_reply_type"
    })
    console.log(status,method,type)
}

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiWechatReplyLists,
    params: queryParams
})



// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}
// 返回
const handleBack = ()=> {
    removeTab(router.currentRoute.value.fullPath)
    router.push('/work/auth_system')
}
// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiWechatReplyDelete({ id })
    getLists()
}

getLists()
getDict()
</script>

<style>
.top {
    display: flex;
    flex-direction: row-reverse;
}
</style>

