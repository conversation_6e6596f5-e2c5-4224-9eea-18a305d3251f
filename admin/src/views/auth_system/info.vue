<template>
    <div class="info-popup">
        <popup
            ref="popupRef"
            :async="true"
            width="550px"
            :confirmButtonText="'复制'"
            @confirm="handleClose"
        >
            <el-form
                :model="formData"
                :label-width="appStore.isMobile ? '80px' : '130px'"
            >

                    <el-form-item label="账户">
                        <div class="flex-1 min-w-0">
                            <div class="sm:flex">
                                <div class="mr-4 sm:w-80 flex">
                                    <el-input v-model="formData.oauth.client" disabled />
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="秘钥">
                        <div class="flex-1 min-w-0">
                            <div class="sm:flex">
                                <div class="mr-4 sm:w-80 flex">
                                    <el-input v-model="formData.oauth.client_secret" disabled />
                                </div>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="接口地址">
                        <div class="flex-1 min-w-0">
                            <div class="sm:flex">
                                <div class="mr-4 sm:w-80 flex">
                                    <el-input value="https://nlpt.tenqent.com/pengking_doc/#home?shareKey=1748573123" disabled />
                                </div>
                            </div>
                            <div class="form-tips">
                                接口默认密码为 ：123456
                            </div>
                        </div>

                    </el-form-item>
<!--                <el-form-item >-->
<!--                <el-button type="primary" v-copy="123">-->
<!--                    复制-->
<!--                </el-button>-->
<!--                </el-form-item>-->
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup >
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiAuthSystemAdd, apiAuthSystemEdit, apiAuthSystemDetail,apiAuthSystemXm } from '@/api/auth_system'
import useAppStore from '@/stores/modules/app'

const appStore = useAppStore()


import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('info')


// 弹窗标题
const popupTitle = computed(() => {
    return mode.value ==='对接信息'
})

// 表单数据
const formData = reactive({
    id: '',
    oauth: '',
    project: '',
})



// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }


}








//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}
console.log(formData)
// 关闭回调
const handleClose = () => {
    copyText()
    emit('close')
}
const copyText = async () => {
    try {
        let text
        text ="系统名称： "+formData.project.name+
            '\n' +
            "系统对接账户： "+formData.oauth.client+
            '\n' +
            '系统对接秘钥： '+formData.oauth.client_secret+
            '\n' +
            '系统对接地址： '+
            'https://nlpt.tenqent.com/pengking_doc/#home?shareKey=1748573123'+
            '\n' +
            '接口文档密码：123456 '
        await navigator.clipboard.writeText(text)
        ElMessage.success('复制成功')
    } catch {
        ElMessage.error('复制失败')
    }
}

defineExpose({
    open,
    setFormData,
    //getDetail
})

</script>
