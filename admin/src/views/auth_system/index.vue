<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
                <el-form-item label="项目" prop="project_id">
                    <el-select class="" v-model="queryParams.project_id" filterable clearable placeholder="请选择实时报项目" with="300">
                        <el-option label="全部" value=""></el-option>
                        <el-option
                            v-for="item in xm"
                            :key="item.project_id"
                            :label="item.project.name"
                            :value="item.project_id"
                        />
                    </el-select>
                </el-form-item>

                <!--                <el-form-item label="回调域名" prop="domain">-->
                <!--                    <el-input class="w-[280px]" v-model="queryParams.domain" clearable placeholder="请输入回调域名" />-->
                <!--                </el-form-item>-->

                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">
            <el-button v-perms="['work.auth_system/add']" type="primary" @click="handleAdd">
                <template #icon>
                    <icon name="el-icon-Plus" />
                </template>
                新增
            </el-button>
            <el-button
                v-perms="['work.auth_system/delete']"
                :disabled="!selectData.length"
                @click="handleDelete(selectData)"
            >
                删除
            </el-button>
            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="30" />
                      <el-table-column label="实时报项目" width="200" prop="project.name" />

                    <el-table-column width="200" label="客户单位名称" prop="project.customer_company"  />
<!--                    <el-table-column label="账户"  prop="oauth.client"  />-->
<!--                    <el-table-column label="秘钥" width="300" prop="oauth.client_secret"  />-->
                    <el-table-column label="项目状态"  prop="xmStatus" />

                  <el-table-column label="登录总次数"  prop="loginCount" />
                  <el-table-column label="PC管理端"  prop="pcCount" />
                  <el-table-column label="微信小程序"  prop="xcxCount" />
                  <el-table-column label="PC大屏"  prop="dpCount" />
                  <el-table-column label="H5" prop="h5Count" />
                  <el-table-column label="扫码失效" prop="scan_code_invalid"  />
                  <el-table-column label="账户失效" prop="code_invalid"  />
                    <el-table-column label="创建时间" prop="create_time"  />
                  <el-table-column label="操作" width="250" fixed="right">
                        <template #default="{ row }">
                            <el-button
                                link
                                @click="handleInfo(row)"
                                type="primary">对接信息
                            </el-button>
<!--                            <el-button-->
<!--                                v-perms="['work.auth_system/edit']"-->
<!--                                type="primary"-->
<!--                                link-->
<!--                                @click="handleReply(row)"-->
<!--                            >-->
<!--                                自定义回复-->
<!--                            </el-button>-->
                            <el-button
                                v-perms="['work.auth_system/edit']"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                v-perms="['work.auth_system/delete']"
                                type="danger"
                                link
                                @click="handleDelete(row.id)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <edit-popup v-if="showEdit" ref="editRef" :dict-data="dictData" @success="getLists" @close="showEdit = false" />
        <info-popup v-if="showInfo" ref="infoRef" :dict-data="dictData" @success="getLists" @close="showInfo = false" />
    </div>
</template>

<script lang="ts" setup name="authSystemLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictData } from '@/hooks/useDictOptions'
import { apiAuthSystemLists, apiAuthSystemDelete, apiAuthSystemXm } from '@/api/auth_system'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'
import InfoPopup from './info.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
const infoRef = shallowRef<InstanceType<typeof InfoPopup>>()
// 是否显示编辑框
const showEdit = ref(false)
const showInfo = ref(false)
console.log(showInfo)
// 字典数据
const dictData = useDictData('auth_system')
const xm = ref()

// 查询条件
const queryParams = reactive({
    project_id: '',
})

// 选中数据
const selectData = ref<any[]>([])

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}



// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiAuthSystemLists,
    params: queryParams
})


// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

const handleReply = async (data: any) => {
    if (!data.oauth?.client) {
        ElMessage.error('请先配置对接信息')
        return
    }
    router.push(`/work/wechat_reply?client=${data.oauth?.client}`)
}
const handleInfo = async (data: any) => {
    try {
        let text
        text ="接入流程： https://nlpt.tenqent.com/doc/#/pages/article/index?guid=97a39d5c9365df4e3ec17fff152b1ac8" +
            '\n' +
            "系统名称： "+data.project.name+
            '\n' +
            "系统对接账户： "+data.oauth.client+
            '\n' +
            '系统对接秘钥： '+data.oauth.client_secret+
            '\n' +
            '接口文档： '+
            'https://nlpt.tenqent.com/pengking_doc/#/home'
        console.log(text)
        await navigator.clipboard.writeText(text)
        ElMessage.success('复制成功')
    } catch {
        ElMessage.error('复制失败')
    }
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiAuthSystemDelete({ id })
    getLists()
}
const getXm = async () => {
    xm.value = await apiAuthSystemXm()
    console.log(xm)
}
getLists()
getXm()
</script>

