<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="120px" :rules="formRules">
                <el-form-item label="项目" prop="project_id">
                    <el-select filterable class="flex-1" v-model="formData.project_id" clearable placeholder="请选择实时报项目">
                        <el-option 
                            v-for="item in xm"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-form-item>
<!--                <el-form-item label="业务域名" prop="domain">-->
<!--                    <el-input v-model="formData.domain" clearable placeholder="请输入业务域名" />-->
<!--                </el-form-item>-->

                <el-form-item label="业务出口公网IP" prop="ip">
                    <el-input v-model="formData.ip" clearable placeholder="请输入IP,多个用英文逗号分割" />
                </el-form-item>
                <el-form-item label="扫码失效时间" prop="scan_code_invalid">
                    <el-date-picker 
                        class="flex-1 !flex"
                        v-model="formData.scan_code_invalid"
                        clearable
                        type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        placeholder="选择扫码失效时间">
                    </el-date-picker>
                </el-form-item>
                
                <el-form-item label="账户失效时间" prop="code_invalid">
                    <el-date-picker 
                        class="flex-1 !flex"
                        v-model="formData.code_invalid"
                        clearable
                        type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        placeholder="选择账户失效时间">
                    </el-date-picker>
                </el-form-item>

            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="authSystemEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiAuthSystemAdd, apiAuthSystemEdit, apiAuthSystemDetail,apiAuthSystemXm,apiAuthSystemProject } from '@/api/auth_system'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')
const xm = ref([])




// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑系统授权' : '新增系统授权'
})

// 表单数据
const formData = reactive({
    id: '',
    project_id: '',
    domain: '',
    ip: '',
    scan_code_invalid: '',
    code_invalid: '',

})


// 表单验证
const formRules = reactive<any>({
    project_id: [{ required: true, message: '项目不能为空', trigger: 'blur' }],
   // domain: [{ required: true, message: '授权域名不能为空', trigger: 'blur' }],
    ip: [{ required: true, message: '系统ip不能为空', trigger: 'blur' }],
    scan_code_invalid: [{ required: true, message: '扫码失效时间不能为空', trigger: 'blur' }],
    code_invalid: [{ required: true, message: '账户失效时间不能为空', trigger: 'blur' }],
})


// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
    
    
}

const getXm = async () => {
    xm.value = await apiAuthSystemProject()
    console.log(formData)
}

const getDetail = async (row: Record<string, any>) => {
    const data = await apiAuthSystemDetail({
        id: row.id
    })
    setFormData(data)
}


// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData,  }
    mode.value == 'edit' 
        ? await apiAuthSystemEdit(data) 
        : await apiAuthSystemAdd(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}



defineExpose({
    open,
    setFormData,
    getDetail
})
getXm()
</script>
