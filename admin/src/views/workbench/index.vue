<template>
    <div class="workbench">
        <div class="lg:flex">
            <el-card class="!border-none mb-4 lg:mr-4 lg:w-[350px]" shadow="never">
                <template #header>
                    <span class="card-title text-xl">接入系统： {{workbenchData.xm.count}}总数</span>
                </template>
                <div>
                    <div class="flex leading-9">
                        <div class="w-60 text-xl">未交付</div>
                        <span class="text-3xl"> {{workbenchData.xm.wjf}}</span>
                    </div>
                    <div class="flex leading-9">
                        <div class="w-60 text-xl">已交付未回款</div>
                        <span class="text-3xl"> {{workbenchData.xm.yjfwhk}}</span>
                    </div>
                    <div class="flex leading-9">
                        <div class="w-60 text-xl">已交付未付尾款</div>
                        <span class="text-3xl"> {{workbenchData.xm.yjfwwk}}</span>
                    </div>
                    <div class="flex leading-9">
                        <div class="w-60 text-xl">已交付运维中</div>
                        <span class="text-3xl">{{workbenchData.xm.yunwei}}</span>
                    </div>
                </div>
            </el-card>
            <el-card class="!border-none mb-4 flex-1" shadow="never">
                <template #header>
                    <div>
                        <span class="card-title">今日数据</span>
                        <span class="text-tx-secondary text-xs ml-4">
                            更新时间：{{ workbenchData.today.time }}
                        </span>
                    </div>
                </template>

                <div class="flex flex-wrap">
                    <div class="w-1/4 md:w-[12.5%]">
                        <div class="leading-10">登录次数</div>
                        <div class="text-5xl">3658</div>
                        <div class="text-tx-secondary text-xs">
                            总：98444635
                        </div>
                    </div>
                    <div class="w-1/4 md:w-[12.5%]">
                        <div class="leading-10">PC登录次数</div>
                        <div class="text-5xl">3985</div>
                        <div class="text-tx-secondary text-xs">
                            总：5897534
                        </div>
                    </div>
                    <div class="w-1/4 md:w-[12.5%]">
                        <div class="leading-10">移动端登录次数</div>
                        <div class="text-5xl">5424</div>
                        <div class="text-tx-secondary text-xs">
                            总：36587554
                        </div>
                    </div>
                    <div class="w-1/4 md:w-[12.5%]">
                        <div class="leading-10">扫码关注用户</div>
                        <div class="text-6xl">865</div>
                        <div class="text-tx-secondary text-xs">
                            总：98756
                        </div>
                    </div>
                    <div class="w-1/4 md:w-[12.5%]">
                        <div class="leading-10">扫码次数</div>
                        <div class="text-5xl">58731</div>
                        <div class="text-tx-secondary text-xs">
                            总：5986834
                        </div>
                    </div>
<!--                    <div class="w-1/4 md:w-[12.5%]">-->
<!--                        <div class="leading-10">自定义回复数</div>-->
<!--                        <div class="text-5xl">9638</div>-->
<!--                        <div class="text-tx-secondary text-xs">-->
<!--                            总：9866854-->
<!--                        </div>-->
<!--                    </div>-->
                    <div class="w-1/4 md:w-1/4">
                        <div class="leading-10">迭代推送数</div>
                        <div class="text-5xl">368934</div>
                        <div class="text-tx-secondary text-xs">
                            总：88754235
                        </div>
                    </div>
                </div>
            </el-card>
        </div>

        <div class="lg:flex gap-4">
            <el-card class="!border-none mb-4 lg:mb-0 w-full" shadow="never">
                <template #header>
                    <span>访问量趋势图</span>
                </template>
                <div>
                    <v-charts
                        ref="visitorChart"
                        style="height: 350px"
                        :option="workbenchData.visitorOption"
                        :autoresize="true"
                    />
                </div>
            </el-card>
        </div>
    </div>
</template>

<script lang="ts" setup name="workbench">
import vCharts from 'vue-echarts'

import { getWorkbench } from '@/api/app'
import useSettingStore from '@/stores/modules/setting'
import { useComponentRef } from '@/utils/getExposeType'
import { calcColor } from '@/utils/util'

const settingStore = useSettingStore()
const saleChart = useComponentRef(vCharts)
const visitorChart = useComponentRef(vCharts)

watch(
    () => settingStore.theme,
    () => {
        updateColor()
    }
)

// 表单数据
const workbenchData: any = reactive({
    xm:[], //项目相关
    support: [],
    today: {}, // 今日数据
    menu: [], // 常用功能
    visitor: [], // 访问量
    article: [], // 文章阅读量

    visitorOption: {
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value'
        },
        legend: {
            data: [
                '登录次数',
                'PC登录次数',
                '移动端登录次数',
                '扫码关注用户',
                '扫码次数',
                '自定义回复数',
                '系统迭代推送数'
            ]
        },
        tooltip: {
            trigger: 'axis'
        },
        series: [
            {
                name: '登录次数',
                data: [],
                type: 'line',
                smooth: true,
                color: '#409EFF',
                lineStyle: {
                    color: '#409EFF',
                    width: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#409EFF'
                            },
                            {
                                offset: 1,
                                color: '#409EFF'
                            }
                        ]
                    },
                    opacity: 0.1
                }
            },
            {
                name: 'PC登录次数',
                data: [],
                type: 'line',
                smooth: true,
                color: '#28C76F',
                lineStyle: {
                    color: '#28C76F',
                    width: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#28C76F'
                            },
                            {
                                offset: 1,
                                color: '#28C76F'
                            }
                        ]
                    },
                    opacity: 0.1
                }
            },
            {
                name: '移动端登录次数',
                data: [],
                type: 'line',
                smooth: true,
                color: '#EA5455',
                lineStyle: {
                    color: '#EA5455',
                    width: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#EA5455'
                            },
                            {
                                offset: 1,
                                color: '#EA5455'
                            }
                        ]
                    },
                    opacity: 0.1
                }
            },
            {
                name: '扫码关注用户',
                data: [],
                type: 'line',
                smooth: true,
                color: '#FF9F43',
                lineStyle: {
                    color: '#FF9F43',
                    width: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#FF9F43'
                            },
                            {
                                offset: 1,
                                color: '#FF9F43'
                            }
                        ]
                    },
                    opacity: 0.1
                }
            },
            {
                name: '扫码次数',
                data: [],
                type: 'line',
                smooth: true,
                color: '#01CFE8',
                lineStyle: {
                    color: '#01CFE8',
                    width: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#01CFE8'
                            },
                            {
                                offset: 1,
                                color: '#01CFE8'
                            }
                        ]
                    },
                    opacity: 0.1
                }
            },
            {
                name: '自定义回复数',
                data: [],
                type: 'line',
                smooth: true,
                color: '#4A5DFF',
                lineStyle: {
                    color: '#4A5DFF',
                    width: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#4A5DFF'
                            },
                            {
                                offset: 1,
                                color: '#4A5DFF'
                            }
                        ]
                    },
                    opacity: 0.1
                }
            },
            {
                name: '系统迭代推送数',
                data: [],
                type: 'line',
                smooth: true,
                color: '#EA5455',
                lineStyle: {
                    color: '#EA5455',
                    width: 2
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#EA5455'
                            },
                            {
                                offset: 1,
                                color: '#EA5455'
                            }
                        ]
                    },
                    opacity: 0.1
                }
            }
        ]
    },

    saleOption: {
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value',
            name: '单位（万）'
        },
        tooltip: {
            trigger: 'axis'
        },
        series: [
            {
                name: '销售量',
                data: [],
                type: 'bar',
                showBackground: true,
                backgroundStyle: {
                    color: 'rgba(180, 180, 180, 0.2)',
                    borderRadius: [10, 10, 0, 0]
                },
                barWidth: '40%',
                itemStyle: {
                    borderRadius: [10, 10, 0, 0],
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: calcColor(settingStore.theme, 0.7)
                            },
                            {
                                offset: 1,
                                color: settingStore.theme
                            }
                        ]
                    }
                }
            }
        ]
    }
})

// 获取工作台主页数据
const getData = () => {
    getWorkbench()
        .then((res: any) => {
            workbenchData.version = res.version
            workbenchData.today = res.today
            workbenchData.menu = res.menu
            workbenchData.visitor = res.visitor
            workbenchData.support = res.support
            workbenchData.xm = res.xm

            // 清空echarts 数据
            workbenchData.visitorOption.xAxis.data = []
            workbenchData.visitorOption.series[0].data = []
            workbenchData.saleOption.xAxis.data = []
            workbenchData.saleOption.series[0].data = []

            // 写入从后台拿来的数据
            res.visitor.date.reverse().forEach((item: any) => {
                workbenchData.visitorOption.xAxis.data.push(item)
            })
            for (let i = 0; i < res.visitor.list.length; i++) {
                res.visitor.list[i].data.forEach((item: any) => {
                    workbenchData.visitorOption.series[i].data.push(item)
                })
            }

            res.sale.date.reverse().forEach((item: any) => {
                workbenchData.saleOption.xAxis.data.push(item)
            })
            res.sale.list[0].data.forEach((item: any) => {
                if (item <= 50) {
                    item = {
                        value: item,
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: calcColor('#ff8729', 0.7)
                                    },
                                    {
                                        offset: 1,
                                        color: '#ff8729'
                                    }
                                ]
                            }
                        }
                    }
                }
                workbenchData.saleOption.series[0].data.push(item)
            })
        })
        .catch((err: any) => {
            console.log('err', err)
        })
}

const updateColor = () => {
    workbenchData.visitorOption.series[0].color = settingStore.theme
    workbenchData.visitorOption.series[0].lineStyle.color = settingStore.theme
    workbenchData.visitorOption.series[0].areaStyle.color.colorStops = [
        {
            offset: 0,
            color: settingStore.theme
        },
        {
            offset: 1,
            color: settingStore.theme
        }
    ]
    workbenchData.saleOption.series[0].itemStyle.color.colorStops = [
        {
            offset: 0,
            color: calcColor(settingStore.theme, 0.7)
        },
        {
            offset: 1,
            color: settingStore.theme
        }
    ]

    saleChart.value?.clear()
    visitorChart.value?.clear()
    saleChart.value?.setOption(workbenchData.saleOption)
    visitorChart.value?.setOption(workbenchData.visitorOption)
}

onMounted(() => {
    getData()
})
</script>

<style lang="scss" scoped></style>
