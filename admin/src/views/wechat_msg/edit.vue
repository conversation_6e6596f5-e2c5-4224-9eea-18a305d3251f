<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="650px"
            :confirmButtonText="mode==='edit'?false :'立即推送' "
            :cancelButtonText="mode==='edit'?false:'取消' "
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
                <el-form-item label="系统名称" prop="client">
                    <el-select class="flex-1" v-model="formData.client" clearable placeholder="系统名称">
                        <el-option
                            v-for="item in xm"
                            :key="item.oauth.client"
                            :label="item.project.name"
                            :value="item.oauth.client"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item label="类型" prop="system_update_type">
                    <el-select class="flex-1" v-model="formData.system_update_type" clearable placeholder="请选择系统更新类型">
                        <el-option 
                            v-for="(item, index) in dictData.system_update_type"
                            :key="index" 
                            :label="item.name"
                            :value="parseInt(item.value)"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="推送内容" prop="content">
                    <editor class="flex-1" v-model="formData.content" :height="500" />
                </el-form-item>

            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="wechatMsgEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiWechatMsgAdd, apiWechatMsgEdit, apiWechatMsgDetail } from '@/api/wechat_msg'
import { apiAuthSystemLists } from '@/api/auth_system'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')
const xm =  ref()

// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '查看' : '推送'
})

// 表单数据
const formData = reactive({
    id: '',
    client: '',
    system_update_type: '',
    content: '',

})


// 表单验证
const formRules = reactive<any>({

})


// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
}

const getDetail = async (row: Record<string, any>) => {
    const data = await apiWechatMsgDetail({
        id: row.id
    })
    setFormData(data)
}

const getXm = async () => {
    const data = await apiAuthSystemLists({})
    xm.value = data.lists || [];
    console.log(xm)
}



// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData,  }
    mode.value == 'edit' 
        ? await apiWechatMsgEdit(data) 
        : await apiWechatMsgAdd(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}



defineExpose({
    open,
    setFormData,
    getDetail
})
getXm()
</script>
