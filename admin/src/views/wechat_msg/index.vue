<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
<!--                <el-form-item label="项目名称" prop="name">-->
<!--                    <el-select class="with:300" v-model="queryParams.name" clearable placeholder="请选择项目名称" >-->
<!--                        <el-option label="全部" value=""></el-option>-->
<!--                        <el-option-->
<!--                            v-for="(item, index) in dictData.system_update_type"-->
<!--                            :key="index"-->
<!--                            :label="item.name"-->
<!--                            :value="item.value"-->
<!--                        />-->
<!--                    </el-select>-->
<!--                </el-form-item>-->

                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">
<!--            <el-button v-perms="['work.wechat_msg/add']" type="primary" @click="handleAdd">-->
<!--                <template #icon>-->
<!--                    <icon name="el-icon-Plus" />-->
<!--                </template>-->
<!--                新增-->
<!--            </el-button>-->
<!--            <el-button-->
<!--                v-perms="['work.wechat_msg/delete']"-->
<!--                :disabled="!selectData.length"-->
<!--                @click="handleDelete(selectData)"-->
<!--            >-->
<!--                删除-->
<!--            </el-button>-->
            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="项目名称" prop="name" />
                    <el-table-column label="更新总次数" prop="update_count" />
                    <el-table-column label="仓库名称" >
                        <template #default="{ row }">
                          <div v-if="row.git?.[0].git">
                              <p v-for="(item, index) in row.git" :key="index">{{ item.git.name }}</p>
                          </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="最后更新人" >
                        <template #default="{ row }">
                            <div v-if="row.git?.[0].git">
                                <p v-for="(item, index) in row.git" :key="index">{{item.commit[0].author_name }}</p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="最后更新时间" >
                        <template #default="{ row }">
                            <div v-if="row.git?.[0].git">
                                <p v-for="(item, index) in row.git" :key="index">{{item.commit[0].committed_date }}</p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200" fixed="right">
                        <template #default="{ row }">
                             <el-button
                                v-perms="['work.wechat_msg/edit']"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                查看详情
                            </el-button>
<!--                            <el-button-->
<!--                                v-perms="['work.wechat_msg/delete']"-->
<!--                                type="danger"-->
<!--                                link-->
<!--                                @click="handleDelete(row.id)"-->
<!--                            >-->
<!--                                删除-->
<!--                            </el-button>-->
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <edit-popup v-if="showEdit" ref="editRef" :dict-data="dictData" @success="getLists" @close="showEdit = false" />
    </div>
</template>

<script lang="ts" setup name="wechatMsgLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictData } from '@/hooks/useDictOptions'
import { apiWechatMsgLists, apiWechatMsgDelete } from '@/api/wechat_msg'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
// 是否显示编辑框
const showEdit = ref(false)


// 查询条件
const queryParams = reactive({
    system_update_type: '',
    content: '',
})

// 选中数据
const selectData = ref<any[]>([])

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}

// 获取字典数据
const { dictData } = useDictData('system_update_type')

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiWechatMsgLists,
    params: queryParams
})

// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiWechatMsgDelete({ id })
    getLists()
}

getLists()
</script>

