<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
                <el-form-item label="通讯录类型" prop="type">
                    <el-select class="" v-model="queryParams.type" clearable placeholder="请选择类型:1好友 2群">
                        <el-option label="全部" value=""></el-option>
                        <el-option 
                            v-for="(item, index) in dictData.msg_source"
                            :key="index" 
                            :label="item.name"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="设备ID" prop="appId">
                    <el-input class="w-[280px]" v-model="queryParams.appId" clearable placeholder="请输入设备ID" />
                </el-form-item>
                <el-form-item label="微信id" prop="wxid">
                    <el-input class="w-[280px]" v-model="queryParams.wxid" clearable placeholder="请输入微信id" />
                </el-form-item>
                <el-form-item label="名称" prop="userName">
                    <el-input class="w-[280px]" v-model="queryParams.userName" clearable placeholder="请输入名称" />
                </el-form-item>
                <el-form-item label="昵称" prop="nickName">
                    <el-input class="w-[280px]" v-model="queryParams.nickName" clearable placeholder="请输入昵称" />
                </el-form-item>
                <el-form-item label="性别" prop="sex">
                    <el-input class="w-[280px]" v-model="queryParams.sex" clearable placeholder="请输入性别" />
                </el-form-item>
                <el-form-item label="手机号列表" prop="phoneNumList">
                    <el-input class="w-[280px]" v-model="queryParams.phoneNumList" clearable placeholder="请输入手机号列表" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">
            <el-button v-perms="['bot.wx_address_book/add']" type="primary" @click="handleAdd">
                <template #icon>
                    <icon name="el-icon-Plus" />
                </template>
                新增
            </el-button>
            <el-button
                v-perms="['bot.wx_address_book/delete']"
                :disabled="!selectData.length"
                @click="handleDelete(selectData)"
            >
                删除
            </el-button>
            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="类型:1好友 2群" prop="type">
                        <template #default="{ row }">
                            <dict-value :options="dictData.msg_source" :value="row.type" />
                        </template>
                    </el-table-column>
                    <el-table-column label="设备ID" prop="appId" show-overflow-tooltip />
                    <el-table-column label="微信id" prop="wxid" show-overflow-tooltip />
                    <el-table-column label="名称" prop="userName" show-overflow-tooltip />
                    <el-table-column label="昵称" prop="nickName" show-overflow-tooltip />
                    <el-table-column label="性别" prop="sex" show-overflow-tooltip />
                    <el-table-column label="备注" prop="remark" show-overflow-tooltip />
                    <el-table-column label="微信号" prop="alias" show-overflow-tooltip />
                    <el-table-column label="大微信头像" prop="bigHeadImgUrl" show-overflow-tooltip />
                    <el-table-column label="手机号列表" prop="phoneNumList" show-overflow-tooltip />

                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
                             <el-button
                                v-perms="['bot.wx_address_book/edit']"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                v-perms="['bot.wx_address_book/delete']"
                                type="danger"
                                link
                                @click="handleDelete(row.id)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <edit-popup v-if="showEdit" ref="editRef" :dict-data="dictData" @success="getLists" @close="showEdit = false" />
    </div>
</template>

<script lang="ts" setup name="wxAddressBookLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictData } from '@/hooks/useDictOptions'
import { apiWxAddressBookLists, apiWxAddressBookDelete } from '@/api/wx_address_book'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
// 是否显示编辑框
const showEdit = ref(false)


// 查询条件
const queryParams = reactive({
    type: '',
    appId: '',
    wxid: '',
    userName: '',
    nickName: '',
    sex: '',
    phoneNumList: '',
})

// 选中数据
const selectData = ref<any[]>([])

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}

// 获取字典数据
const { dictData } = useDictData('msg_source')

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiWxAddressBookLists,
    params: queryParams
})

// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiWxAddressBookDelete({ id })
    getLists()
}

getLists()
</script>

