<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
                <el-form-item label="设备号" prop="imei">
                    <el-input v-model="formData.imei" clearable placeholder="请输入设备号" />
                </el-form-item>
                <el-form-item label="是否禁用" prop="status">
                    <el-radio-group v-model="formData.status" placeholder="请选择是否禁用">
                        <el-radio
                            v-for="(item, index) in dictData.system_disable"
                            :key="index"
                            :label="parseInt(item.value)"
                        >
                            {{ item.name }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="物联网平台" prop="appid">
                    <el-select class="flex-1" v-model="formData.appid" clearable placeholder="请选择物联网平台">
                        <el-option 
                            v-for="(item, index) in iot.lists"
                            :key="index" 
                            :label="item.name"
                            :value="item.appid"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="设备到期时间" prop="expiration_date">
                    <el-date-picker
                        class="flex-1 !flex"
                        v-model="formData.expiration_date"
                        clearable
                        type="datetime"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        placeholder="选择设备到期时间">
                    </el-date-picker>
                </el-form-item>


            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="openDoorDeviceEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiOpenDoorDeviceAdd, apiOpenDoorDeviceEdit, apiOpenDoorDeviceDetail } from '@/api/open_door_device'
import { apiOpenIotLists, apiOpenIotDelete } from '@/api/open_iot'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
import { apiAuthSystemXm } from '@/api/auth_system.ts'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')


// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑4G开门设备' : '新增4G开门设备'
})

// 表单数据
const formData = reactive({
    id: '',
    imei: '',
    status: 0,
    appid: '',
    expiration_date: '',

})


// 表单验证
const formRules = reactive<any>({

})


// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
    
    
}

const getDetail = async (row: Record<string, any>) => {
    const data = await apiOpenDoorDeviceDetail({
        id: row.id
    })
    setFormData(data)
}


// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData,  }
    mode.value == 'edit' 
        ? await apiOpenDoorDeviceEdit(data) 
        : await apiOpenDoorDeviceAdd(data)
    popupRef.value?.close()
    emit('success')
}
const iot = ref([])
const getIot = async () => {
    iot.value = await apiOpenIotLists()
}
//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}



defineExpose({
    open,
    setFormData,
    getDetail
})
getIot()
</script>
