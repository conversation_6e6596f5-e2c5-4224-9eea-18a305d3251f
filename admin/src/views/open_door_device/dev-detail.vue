<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            :confirmButtonText=false
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="120px" :rules="formRules">
                <el-form-item label="激活时间" >
                   <el-input v-model="formData.startDate" disabled />
                </el-form-item>
              <el-form-item label="卡过期时间"   >
                <el-input v-model="formData.expireDate" disabled />
              </el-form-item>
              <el-form-item label="同步时间"   >
                <el-input v-model="formData.lastSyncDate" disabled />
              </el-form-item>
              <el-form-item label="机构的名称"   >
                <el-input v-model="formData.orgName" disabled />
              </el-form-item>

              <el-form-item label="套餐名称"   >
                <el-input v-model="formData.iratePlanName" disabled />
              </el-form-item>
              <el-form-item label="本月用量"  >
                <el-input v-model="formData.dataUsage" disabled />
              </el-form-item>
              <el-form-item label="激活宽限期" >
                <el-input v-model="formData.activeDuration" disabled />
              </el-form-item>
              <el-form-item label="手机号码"   >
                <el-input v-model="formData.msisdn" disabled />
              </el-form-item>
              <el-form-item label="卡备注"   >
                <el-input v-model="formData.memo" disabled />
              </el-form-item>



            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="openDoorDeviceEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiOpenDoorDevicedevDetail } from '@/api/open_door_device'

import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('dev-detail')
console.log(mode)


// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑4G开门设备' : 'SIM卡详情'
})

// 表单数据
const formData = reactive({
    startDate: '',
    expireDate: '',
  lastSyncDate: 0,
  orgName: '',
  memo: '',
  iratePlanName: '',
  dataUsage: '',
  activeDuration: '',
  msisdn: '',
})
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }


}

// 表单验证
const formRules = reactive<any>({

})

const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}
// 获取详情






const iot = ref([])


// 关闭回调
const handleClose = () => {
    emit('close')
}



defineExpose({
    open,
    setFormData,

})


</script>
