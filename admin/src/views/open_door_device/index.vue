<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
                <el-form-item label="设备号" prop="imei">
                    <el-input class="w-[280px]" v-model="queryParams.imei" clearable placeholder="请输入设备号" />
                </el-form-item>
                <el-form-item label="是否启用" prop="status">
                    <el-select class="w-[280px]" v-model="queryParams.status" clearable placeholder="请选择是否启用">
                        <el-option label="全部" value=""></el-option>
                        <el-option
                            v-for="(item, index) in dictData.system_disable"
                            :key="index"
                            :label="item.name"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否在线" prop="status">
                    <el-select class="w-[280px]" v-model="queryParams.is_online" clearable placeholder="请选择是否在线">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="在线" value="1"></el-option>
                        <el-option label="离线" value="0"></el-option>

                    </el-select>
                </el-form-item>
                <el-form-item label="物联网平台" prop="appid">
                    <el-select class="w-[280px]" v-model="queryParams.appid" clearable placeholder="请选择物联网平台">
                        <el-option label="全部" value=""></el-option>
                        <el-option 
                            v-for="(item, index) in iot.lists"
                            :key="index" 
                            :label="item.name"
                            :value="item.appid"
                        />
                    </el-select>
                </el-form-item>
<!--                <el-form-item label="设备到期时间" prop="expiration_date">-->
<!--                    <el-date-picker-->
<!--                        class="flex-1 !flex"-->
<!--                        v-model="queryParams.expiration_date"-->
<!--                        clearable-->
<!--                        type="datetime"-->
<!--                        value-format="YYYY-MM-DD HH:mm:ss"-->
<!--                        placeholder="选择设备到期时间">-->
<!--                    </el-date-picker>-->
<!--                </el-form-item>-->
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">
            <el-button v-perms="['iot.open_door_device/add']" type="primary" @click="handleAdd">
                <template #icon>
                    <icon name="el-icon-Plus" />
                </template>
                新增
            </el-button>
            <el-button
                v-perms="['iot.open_door_device/delete']"
                :disabled="!selectData.length"
                @click="handleDelete(selectData)"
            >
                删除
            </el-button>
            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="设备号" prop="imei" show-overflow-tooltip />
                    <el-table-column label="sim卡ID" prop="iccid" show-overflow-tooltip />
                    <el-table-column label="是否在线" prop="is_online" >
                        <template #default="{ row }">
                            <span :style="{color: row.is_online ? 'green' : 'red'}">
                            {{ row.is_online ? '在线' : '离线' }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="最后发送数据时间" prop="datetime" />
                    <el-table-column label="物联网平台" prop="iot.name">
<!--                        <template #default="{ row }">-->
<!--                            <dict-value :options="dictData.binding" :value="row.appid" />-->
<!--                        </template>-->
                    </el-table-column>
                    <el-table-column label="设备到期时间" prop="expiration_date" show-overflow-tooltip />

                    <el-table-column label="操作" width="200" fixed="right">
                        <template #default="{ row }">

                            <el-button
                                v-if="row.iccid"
                                v-perms="['iot.open_door_device/dev-detail']"
                                type="primary"
                                link
                                @click="handleDevDetail(row)"
                            >
                                查看详情
                            </el-button>
                             <el-button
                                v-perms="['iot.open_door_device/edit']"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                编辑
                            </el-button>
                            <el-button
                                v-perms="['iot.open_door_device/delete']"
                                type="danger"
                                link
                                @click="handleDelete(row.id)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <edit-popup v-if="showEdit" ref="editRef" :dict-data="dictData" @success="getLists" @close="showEdit = false" />
        <dev-detail-popup v-if="showDevDetail" ref="devDetail" :dict-data="dictData" @success="getLists" @close="showDevDetail = false" />
    </div>
</template>

<script lang="ts" setup name="openDoorDeviceLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictData } from '@/hooks/useDictOptions'
import { apiOpenDoorDeviceLists, apiOpenDoorDeviceDelete, apiOpenDoorDevicedevDetail } from '@/api/open_door_device'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'
import DevDetailPopup from './dev-detail.vue'
import {apiOpenIotLists} from "@/api/open_iot.ts";
import DevDetail from '@/views/open_door_device/dev-detail.vue'

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
const devDetail = shallowRef<InstanceType<typeof DevDetailPopup>>()
// 是否显示编辑框
const showEdit = ref(false)
const showDevDetail = ref(false)



// 查询条件
const queryParams = reactive({
    imei: '',
    status: '',
    appid: '',
    expiration_date: '',
})

// 选中数据
const selectData = ref<any[]>([])

const iot = ref([])
const getIot = async () => {
  iot.value = await apiOpenIotLists()
}

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}

// 获取字典数据
const { dictData } = useDictData('system_disable')

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiOpenDoorDeviceLists,
    params: queryParams
})

// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

const handleDevDetail = async (data: any) => {
    showDevDetail.value = true
    await nextTick()
    devDetail.value?.open('dev-detail')

    const abc = await apiOpenDoorDevicedevDetail({
        iccid: data.iccid
    })
    console.log(abc)
    devDetail.value?.setFormData(abc)
    //devDetail.value?.setFormData(abc)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiOpenDoorDeviceDelete({ id })
    getLists()
}

getLists()
getIot()
</script>

