<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
                <el-form-item label="名称" prop="title">
                    <el-input class="w-[280px]" v-model="queryParams.title" clearable placeholder="请输入名称" />
                </el-form-item>

                <el-form-item label="是否显示" prop="status">
                    <el-select class="w-[280px]" v-model="queryParams.status" clearable placeholder="请选择是否显示">
                        <el-option label="全部" value=""></el-option>
                        <el-option 
                            v-for="(item, index) in dictData.show_status"
                            :key="index" 
                            :label="item.name"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">
            <el-button v-perms="['doc.doc/add']" type="primary" @click="handleAdd">
                <template #icon>
                    <icon name="el-icon-Plus" />
                </template>
                新增
            </el-button>
            <el-button
                v-perms="['doc.doc/delete']"
                :disabled="!selectData.length"
                @click="handleDelete(selectData)"
            >
                删除
            </el-button>
            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column @click="handleDoc({ row })" label="名称" prop="title" show-overflow-tooltip />
                    <el-table-column label="备注" prop="mark" show-overflow-tooltip />
                    <el-table-column label="是否显示" min-width="100" v-perms="['doc.doc/edit']">
                        <template #default="{ row }">
                            <el-switch
                                v-model="row.status"
                                :active-value="1"
                                :inactive-value="0"
                                @change="changeStatus(row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column label="封面图片" prop="image">
                        <template #default="{ row }">
                            <el-image style="width:50px;height:50px;" :src="row.image" />
                        </template>
                    </el-table-column>
                    <el-table-column label="排序" prop="sort" show-overflow-tooltip />
                    <el-table-column label="阅读量" prop="read_num" show-overflow-tooltip />

                    <el-table-column label="操作" width="340" fixed="right">
                        <template #default="{ row }">
                            <el-button
                                v-perms="['doc.doc_category/edit', 'doc.doc_category/add:edit']"
                                type="primary"
                                link
                                @click="handleDoc(row)"
                            >
                                内容编辑
                            </el-button>
                            <el-button
                                v-perms="['doc.doc/copy']"
                                type="primary"
                                link
                                @click="handleInfo(row)"
                            >
                                链接分享
                            </el-button>

                             <el-button
                                v-perms="['doc.doc/edit']"
                                type="primary"
                                link
                                @click="handleEdit(row)"
                            >
                                 文档设置
                            </el-button>
                            <el-button
                                v-perms="['doc.doc/delete']"
                                type="danger"
                                link
                                @click="handleDelete(row.id)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <edit-popup v-if="showEdit" ref="editRef" :dict-data="dictData" @success="getLists" @close="showEdit = false" />
    </div>
</template>

<script lang="ts" setup name="docLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictData } from '@/hooks/useDictOptions'
import { apiDocLists, apiDocDelete,apiDocEdit } from '@/api/doc'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'
import router, { getRoutePath } from '@/router'
import { adminEdit } from '@/api/perms/admin.ts'

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
// 是否显示编辑框
const showEdit = ref(false)


// 查询条件
const queryParams = reactive({
    title: '',
    mark: '',
    status: '',
})

// 选中数据
const selectData = ref<any[]>([])
const handleDoc = async (data: any) => {
    //router.push(`/work/wechat_reply?client=${data.oauth?.client}`)
    router.push(`/doc/doc_category?doc_id=${data.id}&guid=${data.guid}`)
}
const changeStatus = (data: any) => {
    apiDocEdit({
        id: data.id,
        title: data.title,
        mark: data.mark,
        image: data.image,
        sort: data.sort,
        status:data.status,
    }).finally(() => {
        getLists()
    })
}
const url =ref()
const handleInfo = async (data: any) => {
    try {

        url.value = "https://nlpt.tenqent.com/doc/#/pages/article/index?guid="+data.guid
        console.log(url)
        await navigator.clipboard.writeText(url.value)
        ElMessage.success('复制成功')
    } catch {
        ElMessage.error('复制失败')
    }
}
// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}

// 获取字典数据
const { dictData } = useDictData('show_status')
console.log(dictData)

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiDocLists,
    params: queryParams
})

// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiDocDelete({ id })
    getLists()
}

getLists()
</script>

