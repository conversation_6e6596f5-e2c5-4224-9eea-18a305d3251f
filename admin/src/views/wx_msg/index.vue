<template>
    <div>
        <el-card class="!border-none mb-4" shadow="never">
            <el-form
                class="mb-[-16px]"
                :model="queryParams"
                inline
            >
                <el-form-item label="发送人的微信ID" prop="wcId">
                    <el-input class="w-[280px]" v-model="queryParams.wcId" clearable placeholder="请输入发送人的微信ID" />
                </el-form-item>
                <el-form-item label="消息类型" prop="messageType">
                    <el-select class="w-[280px]" v-model="queryParams.messageType" clearable placeholder="请选择消息类型">
                        <el-option label="全部" value=""></el-option>
                        <el-option 
                            v-for="(item, index) in dictData.msg_type"
                            :key="index" 
                            :label="item.name"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="消息内容" prop="data">
                    <el-input class="w-[280px]" v-model="queryParams.data" clearable placeholder="请输入消息内容" />
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="!border-none" v-loading="pager.loading" shadow="never">

            <el-button
                v-perms="['bot.wx_msg/delete']"
                :disabled="!selectData.length"
                @click="handleDelete(selectData)"
            >
                删除
            </el-button>
            <div class="mt-4">
                <el-table :data="pager.lists" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="消息来源" prop="wx_address_book" show-overflow-tooltip />
                    <el-table-column label="消息类型" prop="messageType">
                        <template #default="{ row }">
                            <dict-value :options="dictData.msg_type" :value="row.messageType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="消息内容" prop="data.content" show-overflow-tooltip />

                    <el-table-column label="操作" width="120" fixed="right">
                        <template #default="{ row }">
<!--                             <el-button-->
<!--                                v-perms="['bot.wx_msg/edit']"-->
<!--                                type="primary"-->
<!--                                link-->
<!--                                @click="handleEdit(row)"-->
<!--                            >-->
<!--                                编辑-->
<!--                            </el-button>-->
                            <el-button
                                v-perms="['bot.wx_msg/delete']"
                                type="danger"
                                link
                                @click="handleDelete(row.id)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex mt-4 justify-end">
                <pagination v-model="pager" @change="getLists" />
            </div>
        </el-card>
        <edit-popup v-if="showEdit" ref="editRef" :dict-data="dictData" @success="getLists" @close="showEdit = false" />
    </div>
</template>

<script lang="ts" setup name="wxMsgLists">
import { usePaging } from '@/hooks/usePaging'
import { useDictData } from '@/hooks/useDictOptions'
import { apiWxMsgLists, apiWxMsgDelete } from '@/api/wx_msg'
import { timeFormat } from '@/utils/util'
import feedback from '@/utils/feedback'
import EditPopup from './edit.vue'

const editRef = shallowRef<InstanceType<typeof EditPopup>>()
// 是否显示编辑框
const showEdit = ref(false)


// 查询条件
const queryParams = reactive({
    wcId: '',
    messageType: '',
    data: '',
})

// 选中数据
const selectData = ref<any[]>([])

// 表格选择后回调事件
const handleSelectionChange = (val: any[]) => {
    selectData.value = val.map(({ id }) => id)
}

// 获取字典数据
const { dictData } = useDictData('msg_type')

// 分页相关
const { pager, getLists, resetParams, resetPage } = usePaging({
    fetchFun: apiWxMsgLists,
    params: queryParams
})

// 添加
const handleAdd = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('add')
}

// 编辑
const handleEdit = async (data: any) => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open('edit')
    editRef.value?.setFormData(data)
}

// 删除
const handleDelete = async (id: number | any[]) => {
    await feedback.confirm('确定要删除？')
    await apiWxMsgDelete({ id })
    getLists()
}
console.log();
getLists(getLists())
</script>

