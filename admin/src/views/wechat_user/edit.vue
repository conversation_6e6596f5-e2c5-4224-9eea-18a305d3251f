<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
                <el-form-item label="openid" prop="openid">
                    <el-input v-model="formData.openid" clearable placeholder="请输入openid" />
                </el-form-item>
                <el-form-item label="系统ID" prop="client">
                    <el-input v-model="formData.client" clearable placeholder="请输入系统ID" />
                </el-form-item>

            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="wechatUserEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiWechatUserAdd, apiWechatUserEdit, apiWechatUserDetail } from '@/api/wechat_user'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')


// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑微信扫码用户' : '新增微信扫码用户'
})

// 表单数据
const formData = reactive({
    id: '',
    openid: '',
    client: '',

})


// 表单验证
const formRules = reactive<any>({
    openid: [{
        required: true,
        message: '请输入openid',
        trigger: ['blur']
    }],
})


// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
    
    
}

const getDetail = async (row: Record<string, any>) => {
    const data = await apiWechatUserDetail({
        id: row.id
    })
    setFormData(data)
}


// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData,  }
    mode.value == 'edit' 
        ? await apiWechatUserEdit(data) 
        : await apiWechatUserAdd(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}



defineExpose({
    open,
    setFormData,
    getDetail
})
</script>
