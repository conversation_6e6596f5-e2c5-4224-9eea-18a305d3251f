<template>
  <div class="h-full">
      <el-card class="!border-none" shadow="never">
              <el-page-header  :content="$route.meta.title" @back="$router.back()">
                  <el-button style="float:right"  @click="handleView" type="primary" icon="view"  plain>预览</el-button>
              </el-page-header>
      </el-card>
      <el-card class="mt-4 !border-none" shadow="never">
    <el-row :gutter="24" class="pl-3">
      <el-col 
        :span="6"
        style="height:calc(100vh - var(--navbar-height) - 170px);overflow-y: auto;background-color: #FFF;" >
        <el-card class="!border-none "  shadow="never">
            <div>
                <el-button @click="handleExpand"> 展开/折叠 </el-button>
            </div>
             <el-tree
              ref="treeRef"
              :default-expand-all="isExpandAll"
              :data="lists"
              :props="defaultProps "
              :draggable="true"
              :expand-on-click-node="false"
              @node-drop="handleDragEnd"
              @node-click="handleNodeClick"

              class="mt-2"
            >
              <template #default="{ node, data }">
                <div class="custom-tree-node" >
                  <div >{{ data.title }}</div>
                  <div class="flex items-center"> 
                    <el-switch
                        v-model="data.status"
                        :active-value="1"
                        :inactive-value="0"
                        @change="changeStatus(data)"
                        class="mr-4"
                    />
                    <popover-input @confirm="onConfirm($event,data,'add')" class="mr-2">
                        <template #default>
                             <el-icon ><Plus /></el-icon>
                        </template>
                    </popover-input>
                    <popover-input @confirm="onConfirm($event,data,'edit')" :value="data.title" class="mr-2">
                        <template #default>
                             <el-icon ><Edit /></el-icon>
                        </template>
                    </popover-input>
                    <el-icon><Delete @click="handleDeleteDept(data)" /></el-icon>
                  </div>
                </div>
              </template>
            </el-tree>
        
        </el-card>

      </el-col>
      <el-col :span="16" >
        <editor v-model="formData.content" style="height:calc(100vh - var(--navbar-height) - 250px)"/>
          <div class="mt-4" style="text-align: center">
              <el-button  type="primary" @click="handleSave()">保存</el-button>
          </div>

      </el-col>
    </el-row>
      </el-card>
  </div>
</template>
<script lang="ts" setup name="department">
import type { ElTable, FormInstance } from 'element-plus'
import { deptDelete, deptLists } from '@/api/org/department'
import feedback from '@/utils/feedback'
import { apiDocCategoryEdit } from '@/api/doc_category.ts'
import type { RenderContentContext, RenderContentFunction } from 'element-plus'
import useMultipleTabs from '@/hooks/useMultipleTabs'
import { useRoute,useRouter } from 'vue-router'
import {apiGetList, apiPostList} from '@/api/app.ts'

const { removeTab } = useMultipleTabs()
const router = useRouter()
const route = useRoute()

type Node = RenderContentContext['node']
type Data = RenderContentContext['data']

const tableRef = shallowRef<InstanceType<typeof ElTable>>()
const editRef = shallowRef<InstanceType<typeof EditPopup>>()
const formRef = shallowRef<FormInstance>()
let isExpand = false
const treeRef = ref(null)

const loading = ref(false)
const lists = ref<any[]>([])
const queryParams = reactive({
    doc_id:String,
})
// 是否展开全部
const isExpandAll = ref(true)
const formData = reactive({
    content: '',
    id:''
})

const debounce = (fn:any, delay:any)=> {
    // 维护一个 timer，用来记录当前执行函数状态
    let timer = null;
    return function() {
        // 通过 ‘this’ 和 ‘arguments’ 获取函数的作用域和变量
        let context = this;
        let args = arguments;
        // 清理掉正在执行的函数，并重新执行
        clearTimeout(timer);
        timer = setTimeout(function() {
            fn.apply(context, args);
        }, delay);
    }
}

watch(
    ()=> formData.content,
    (newVal, oldVal) => {
        debouncedHandleChange()
    }
)



const defaultProps = { children: 'children', label: 'name' }
const showEdit = ref(false)
const changeStatus = (data: any) => {
    apiDocCategoryEdit({
        id: data.id,
        title: data.title,
        status: data.status,
        doc_id: data.doc_id,
    }).finally(() => {
        getLists()
    })
}



// 拖拽
const handleDragEnd = async (
  draggingNode: any,
  dropNode: any,  // 移动的
  dropType: any,  // 被移动的
  ev: any
) => {
  console.log('tree drag end:',draggingNode.data,dropNode.data,dropType)
  debugger
  const res = await apiPostList('/doc.doc_category/sort', {
        drag_id: draggingNode.data.id,
        target_id: dropNode.data.id||0,
        type: dropType
  })
  getLists()
}

const onConfirm = async (e: any, obj: any,type: string) => {
  if(type === 'add') {
    await apiPostList('/doc.doc_category/add_title', {
      category_id: obj.id,
      doc_id: queryParams.doc_id,
      title:e
    })
    await getLists()
  }else {
    await apiPostList('/doc.doc_category/edit', {
      doc_id: queryParams.doc_id,
      title:e,
      id: obj.id
    })
    await getLists()
  }
  
}
// 删除
const handleDeleteDept = async (obj: any)=> {
  await feedback.confirm('确定要删除？')
  await apiPostList('/doc.doc_category/delete',{ id: obj.id })
  getLists()
}

// 点击
// const handleClick = async (obj: any)=> {
//     console.log(obj)
//     formData.content = obj.content
//     formData.id = obj.id
// }

const handleNodeClick = (e:any)=> {
  formData.id = e.id
  formData.content = e.content??''
    console.log(e)
}
const getLists = async () => {
    loading.value = true
    const res = await apiGetList('/doc.doc_category/lists',queryParams)
    lists.value = res.lists
    if(!formData.id) {
        formData.id = lists.value && lists.value[0].id
        formData.content = lists.value && lists.value[0].content
    }
    loading.value = false
}

const resetParams = () => {
    formRef.value?.resetFields()
    getLists()
}

const handleSave = async () => {
    console.log(formData)
    await apiPostList('/doc.doc_category/editContent', {
      id: formData.id,
      content:formData.content
    })
    await getLists()
}
//
const debouncedHandleChange = debounce();



const handleExpand = async () => {
  isExpandAll.value = !isExpandAll.value

   if(isExpandAll.value) {
      const nodes = treeRef.value.store._getAllNodes();
      nodes.forEach(item => {
        item.expanded = true;
      });
    } else {
      const nodes = treeRef.value.store._getAllNodes();
      nodes.forEach(item => {
        item.expanded = false;
      });
    }

  // console.log( isExpandAll.value,'- isExpandAll.value');
  // await getLists()
    // isExpand = !isExpand
    // toggleExpand(lists.value, isExpand)
}

const toggleExpand = (children: any[], unfold = true) => {
    for (const key in children) {
        tableRef.value?.toggleRowExpansion(children[key], unfold)
        if (children[key].children) {
            toggleExpand(children[key].children!, unfold)
        }
    }
}

onMounted(async () => {
  console.log(route,'--route--');
  queryParams.doc_id = route.query.doc_id??''
  await getLists()
  // nextTick(() => {
  //     handleExpand()
  // })
})

// 返回
const handleView = ()=> {
    console.log(route.query.guid,'--route--');
    window.open(`https://nlpt.tenqent.com/doc/#/pages/article/index?guid=${route.query.guid}`, '_blank')
    //router.push('/doc/doc')
}
</script>


<style scoped>
:deep(.custom-tree-node) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  font-size: 14px;
  padding-right: 8px;
}
.sub-btn {
  display: flex;
  flex-direction: row-reverse;
}
.card-back {
  display: flex;
  flex-direction: row-reverse;
}
:deep(.w-e-text-container [data-slate-editor] .w-e-selected-image-container){
  overflow:clip !important;
}
</style>
