<template>
    <div class="xl:flex">
        <!-- 左侧树形菜单 -->
        <div class="left-tree w-80">
            <el-tree
                :data="treeData"
                node-key="id"
                :props="defaultProps"
                @node-click="handleNodeClick"
                :expand-on-click-node="false">
                <template #default="{ node, data }">
          <span class="custom-node">
            {{ node.label }}
            <el-button
                size="mini"
                @click.stop="addNode(data)"
                icon="Plus">
            </el-button>
          </span>
                </template>
            </el-tree>
        </div>

        <!-- 右侧富文本编辑区 -->
        <div class="right-editor">
            <el-card v-if="currentNode">
                <template #header>
                    <span>{{ currentNode.label }} 内容编辑</span>
                </template>
                <editor v-model="currentContent" :height="667" />
<!--                <el-input-->
<!--                    v-model="currentContent"-->
<!--                    type="textarea"-->
<!--                    :rows="20"-->
<!--                    placeholder="请输入内容"/>-->
            </el-card>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const treeData = ref([
    {
        id: 1,
        label: '根节点',
        content: '',
        children: []
    }
])

const defaultProps = {
    children: 'children',
    label: 'label'
}

const currentNode = ref(null)
const currentContent = ref('')

const handleNodeClick = (data) => {
    currentNode.value = data
    currentContent.value = data.content || ''
}

const addNode = (parentData) => {
    const newNode = {
        id: Date.now(),
        label: `新节点${parentData.children.length + 1}`,
        content: '',
        children: []
    }
    if (!parentData.children) {
        parentData.children = []
    }
    parentData.children.push(newNode)
}
</script>

<style scoped>
.container {
    display: flex;
    height: 100vh;
}
.left-tree {
    width: 250px;
    border-right: 1px solid #eee;
    padding: 20px;
    overflow: auto;
}
.right-editor {
    flex: 1;
    padding: 20px;
}
.custom-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}
</style>
