<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
                <el-form-item label="排序" prop="sort">
                    <el-input v-model="formData.sort" clearable placeholder="请输入排序" />
                </el-form-item>
                <el-form-item label="标题" prop="title">
                    <el-input v-model="formData.title" clearable placeholder="请输入标题" />
                </el-form-item>
                <el-form-item label="内容" prop="content">
                    <editor class="flex-1" v-model="formData.content" :height="500" />
                </el-form-item>
                <el-form-item label="章节" prop="category_id">
                    <el-tree-select
                        class="flex-1"
                        v-model="formData.category_id"
                        :data="treeList"
                        clearable
                        node-key="id"
                        :props="{ label: 'title', value: 'id', children: 'children' }"
                        :default-expand-all="true"
                        placeholder="请选择章节"
                        check-strictly
                    />
                </el-form-item>
                <el-form-item label="是否显示" prop="status">
                    <el-radio-group v-model="formData.status" placeholder="请选择是否显示">
                        <el-radio 
                            v-for="(item, index) in dictData.show_status"
                            :key="index"
                            :label="parseInt(item.value)"
                        >
                            {{ item.name }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>

            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="docCategoryEdit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import { apiDocCategoryLists, apiDocCategoryAdd, apiDocCategoryEdit, apiDocCategoryDetail } from '@/api/doc_category'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')
const treeList = ref<any[]>([])

// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑文档章节' : '新增文档章节'
})

// 表单数据
const formData = reactive({
    id: '',
    sort: '',
    title: '',
    content: '',
    category_id: '',
    status: '',

})


// 表单验证
const formRules = reactive<any>({
    title: [{
        required: true,
        message: '请输入标题',
        trigger: ['blur']
    }],
    content: [{
        required: true,
        message: '请输入内容',
        trigger: ['blur']
    }],
})


// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
    
    
}

const getDetail = async (row: Record<string, any>) => {
    const data = await apiDocCategoryDetail({
        id: row.id
    })
    setFormData(data)
}


// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData,  }
    mode.value == 'edit' 
        ? await apiDocCategoryEdit(data) 
        : await apiDocCategoryAdd(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}

const getLists = async () => {
   const data: any = await apiDocCategoryLists()
   const item = { id: 0, title: '顶级', children: [] }
   item.children = data.lists
   treeList.value.push(item)
}

getLists()

defineExpose({
    open,
    setFormData,
    getDetail
})
</script>
