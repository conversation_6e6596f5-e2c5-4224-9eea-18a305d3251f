import request from '@/utils/request'

// 公众号消息推送列表
export function apiWechatMsgLists(params: any) {
    return request.get({ url: '/work.wechat_msg/lists', params })
}

// 添加公众号消息推送
export function apiWechatMsgAdd(params: any) {
    return request.post({ url: '/work.wechat_msg/add', params })
}

// 编辑公众号消息推送
export function apiWechatMsgEdit(params: any) {
    return request.post({ url: '/work.wechat_msg/edit', params })
}

// 删除公众号消息推送
export function apiWechatMsgDelete(params: any) {
    return request.post({ url: '/work.wechat_msg/delete', params })
}

// 公众号消息推送详情
export function apiWechatMsgDetail(params: any) {
    return request.get({ url: '/work.wechat_msg/detail', params })
}

