import request from '@/utils/request'

// 微信登录信息列表
export function apiWxLoginLists(params: any) {
    return request.get({ url: '/bot.wx_login/lists', params })
}





// 删除微信登录信息
export function apiWxLoginDelete(params: any) {
    return request.post({ url: '/bot.wx_login/delete', params })
}




// 微信登录信息详情
export function createCode(params: any) {
    return request.post({ url: '/bot.wx_login/createCode', params })
}


export function codeLogin(params: any) {
    return request.post({ url: '/bot.wx_login/codeLogin', params ,timeout:8000})
}


export function exitLogin(params: any) {
    return request.post({ url: '/bot.wx_login/exitLogin', params })
}

export function isOnlineClick(params: any) {
    return request.post({ url: '/bot.wx_login/isOnlineClick', params })
}