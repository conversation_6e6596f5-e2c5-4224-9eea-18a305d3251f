import request from '@/utils/request'

export function getRechargeConfig() {
    return request.get({ url: '/recharge.recharge/getConfig' })
}

// 设置
export function setRechargeConfig(params: any) {
    return request.post({ url: '/recharge.recharge/setConfig', params })
}

export function uploadImage(params: any) {
    return request.post({
        url: '/upload/image',
        params,
        headers: {
            'Content-Type': "application/x-www-form-urlencoded;charset=UTF-8",
        }
    })
}





