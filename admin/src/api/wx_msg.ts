import request from '@/utils/request'

// 微信接收消息列表
export function apiWxMsgLists(params: any) {
    return request.get({ url: '/bot.wx_msg/lists', params })
}

// 添加微信接收消息
export function apiWxMsgAdd(params: any) {
    return request.post({ url: '/bot.wx_msg/add', params })
}

// 编辑微信接收消息
export function apiWxMsgEdit(params: any) {
    return request.post({ url: '/bot.wx_msg/edit', params })
}

// 删除微信接收消息
export function apiWxMsgDelete(params: any) {
    return request.post({ url: '/bot.wx_msg/delete', params })
}

// 微信接收消息详情
export function apiWxMsgDetail(params: any) {
    return request.get({ url: '/bot.wx_msg/detail', params })
}