import request from '@/utils/request'

// 文档列表
export function apiDocLists(params: any) {
    return request.get({ url: '/doc.doc/lists', params })
}

// 添加文档
export function apiDocAdd(params: any) {
    return request.post({ url: '/doc.doc/add', params })
}

// 编辑文档
export function apiDocEdit(params: any) {
    return request.post({ url: '/doc.doc/edit', params })
}

// 删除文档
export function apiDocDelete(params: any) {
    return request.post({ url: '/doc.doc/delete', params })
}

// 文档详情
export function apiDocDetail(params: any) {
    return request.get({ url: '/doc.doc/detail', params })
}