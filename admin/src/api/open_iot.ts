import request from '@/utils/request'

// 物联网授权平台列表
export function apiOpenIotLists(params: any) {
    return request.get({ url: '/iot.open_iot/lists', params })
}

// 添加物联网授权平台
export function apiOpenIotAdd(params: any) {
    return request.post({ url: '/iot.open_iot/add', params })
}

// 编辑物联网授权平台
export function apiOpenIotEdit(params: any) {
    return request.post({ url: '/iot.open_iot/edit', params })
}

// 删除物联网授权平台
export function apiOpenIotDelete(params: any) {
    return request.post({ url: '/iot.open_iot/delete', params })
}

// 物联网授权平台详情
export function apiOpenIotDetail(params: any) {
    return request.get({ url: '/iot.open_iot/detail', params })
}