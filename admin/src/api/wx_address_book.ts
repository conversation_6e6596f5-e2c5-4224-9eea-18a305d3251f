import request from '@/utils/request'

// 微信通讯录列表
export function apiWxAddressBookLists(params: any) {
    return request.get({ url: '/bot.wx_address_book/lists', params })
}

// 添加微信通讯录
export function apiWxAddressBookAdd(params: any) {
    return request.post({ url: '/bot.wx_address_book/add', params })
}

// 编辑微信通讯录
export function apiWxAddressBookEdit(params: any) {
    return request.post({ url: '/bot.wx_address_book/edit', params })
}

// 删除微信通讯录
export function apiWxAddressBookDelete(params: any) {
    return request.post({ url: '/bot.wx_address_book/delete', params })
}

// 微信通讯录详情
export function apiWxAddressBookDetail(params: any) {
    return request.get({ url: '/bot.wx_address_book/detail', params })
}