import request from '@/utils/request'

// 微信扫码用户列表
export function apiWechatUserLists(params: any) {
    return request.get({ url: '/work.wechat_user/lists', params })
}

// 添加微信扫码用户
export function apiWechatUserAdd(params: any) {
    return request.post({ url: '/work.wechat_user/add', params })
}

// 编辑微信扫码用户
export function apiWechatUserEdit(params: any) {
    return request.post({ url: '/work.wechat_user/edit', params })
}

// 删除微信扫码用户
export function apiWechatUserDelete(params: any) {
    return request.post({ url: '/work.wechat_user/delete', params })
}

// 微信扫码用户详情
export function apiWechatUserDetail(params: any) {
    return request.get({ url: '/work.wechat_user/detail', params })
}