import request from '@/utils/request'

// 系统授权列表
export function apiAuthSystemLists(params: any) {
    return request.get({ url: '/work.auth_system/lists', params })
}

// 添加系统授权
export function apiAuthSystemAdd(params: any) {
    return request.post({ url: '/work.auth_system/add', params })
}

// 编辑系统授权
export function apiAuthSystemEdit(params: any) {
    return request.post({ url: '/work.auth_system/edit', params })
}

// 删除系统授权
export function apiAuthSystemDelete(params: any) {
    return request.post({ url: '/work.auth_system/delete', params })
}

// 系统授权详情
export function apiAuthSystemDetail(params: any) {
    return request.get({ url: '/work.auth_system/detail', params })
}

// 获取系统列表
export function apiAuthSystemXm() {
    return request.get({ url: '/work.auth_system/xm' })
}
// 获取实时报列表
export function apiAuthSystemProject() {
    return request.get({ url: '/work.auth_system/project' })
}