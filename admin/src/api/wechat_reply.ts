import request from '@/utils/request'

// 自定义回复列表
export function apiWechatReplyLists(params: any) {
    return request.get({ url: '/work.wechat_reply/lists', params })
}

// 添加自定义回复
export function apiWechatReplyAdd(params: any) {
    return request.post({ url: '/work.wechat_reply/add', params })
}

// 编辑自定义回复
export function apiWechatReplyEdit(params: any) {
    return request.post({ url: '/work.wechat_reply/edit', params })
}

// 删除自定义回复
export function apiWechatReplyDelete(params: any) {
    return request.post({ url: '/work.wechat_reply/delete', params })
}

// 自定义回复详情
export function apiWechatReplyDetail(params: any) {
    return request.get({ url: '/work.wechat_reply/detail', params })
}