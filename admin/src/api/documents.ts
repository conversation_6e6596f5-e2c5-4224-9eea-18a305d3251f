import request from '@/utils/request'

// 文档列表
export function apiDocumentsLists(params: any) {
    return request.get({ url: '/doc.documents/lists', params })
}

// 添加文档
export function apiDocumentsAdd(params: any) {
    return request.post({ url: '/doc.documents/add', params })
}

// 编辑文档
export function apiDocumentsEdit(params: any) {
    return request.post({ url: '/doc.documents/edit', params })
}

// 删除文档
export function apiDocumentsDelete(params: any) {
    return request.post({ url: '/doc.documents/delete', params })
}

// 文档详情
export function apiDocumentsDetail(params: any) {
    return request.get({ url: '/doc.documents/detail', params })
}