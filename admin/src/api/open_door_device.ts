import request from '@/utils/request'

// 4G开门设备列表
export function apiOpenDoorDeviceLists(params: any) {
    return request.get({ url: '/iot.open_door_device/lists', params })
}

// 添加4G开门设备
export function apiOpenDoorDeviceAdd(params: any) {
    return request.post({ url: '/iot.open_door_device/add', params })
}

// 编辑4G开门设备
export function apiOpenDoorDeviceEdit(params: any) {
    return request.post({ url: '/iot.open_door_device/edit', params })
}

// 删除4G开门设备
export function apiOpenDoorDeviceDelete(params: any) {
    return request.post({ url: '/iot.open_door_device/delete', params })
}

// 4G开门设备详情
export function apiOpenDoorDeviceDetail(params: any) {
    return request.get({ url: '/iot.open_door_device/detail', params })
}

// 4G开门设备详情
export function apiOpenDoorDevicedevDetail(params: any) {
    return request.get({ url: '/iot.open_door_device/devDetail', params })
}

