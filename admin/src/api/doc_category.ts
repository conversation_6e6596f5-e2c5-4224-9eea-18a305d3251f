import request from '@/utils/request'

// 文档章节列表
export function apiDocCategoryLists(params: any) {
    return request.get({ url: '/doc.doc_category/lists', params })
}

// 添加文档章节
export function apiDocCategoryAdd(params: any) {
    return request.post({ url: '/doc.doc_category/add', params })
}

// 编辑文档章节
export function apiDocCategoryEdit(params: any) {
    return request.post({ url: '/doc.doc_category/edit', params })
}

// 删除文档章节
export function apiDocCategoryDelete(params: any) {
    return request.post({ url: '/doc.doc_category/delete', params })
}

// 文档章节详情
export function apiDocCategoryDetail(params: any) {
    return request.get({ url: '/doc.doc_category/detail', params })
}
