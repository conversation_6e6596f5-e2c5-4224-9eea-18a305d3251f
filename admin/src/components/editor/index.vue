<template>
    <div class="border border-br flex flex-col" :style="styles">
        <toolbar
            class="border-b border-br"
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            :mode="mode"
        />
        <w-editor
            class="flex-1 overflow-hidden"
            v-model="valueHtml"
            :defaultConfig="editorConfig"
            :mode="mode"
            @onCreated="handleCreated"
            @onChange="handleEditorChange"
        />
        <material-picker
            ref="materialPickerRef"
            :type="fileType"
            :limit="-1"
            hidden-upload
            @change="selectChange"
        />
    </div>
</template>
<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { addUnit } from '@/utils/util'
import { uploadImage as UploadImages } from '@/api/app/recharge.ts'
import { Editor as WEditor, Toolbar } from '@wangeditor/editor-for-vue'
import MaterialPicker from '@/components/material/picker.vue'
import type { CSSProperties } from 'vue'
import type { IEditorConfig, IToolbarConfig } from '@wangeditor/editor'

const props = withDefaults(
    defineProps<{
        modelValue?: string
        mode?: 'default' | 'simple'
        height?: string | number
        width?: string | number
        toolbarConfig?: Partial<IToolbarConfig>
    }>(),
    {
        modelValue: '',
        mode: 'default',
        height: '100%',
        width: 'auto',
        toolbarConfig: () => ({})
    }
)

const emit = defineEmits<{
    (event: 'update:modelValue', value: string): void
}>()

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const materialPickerRef = shallowRef<InstanceType<typeof MaterialPicker>>()
const fileType = ref('')
let isProcessing = false // 添加处理标记
let isInitialized = false // 添加初始化标记
let lastImageData = new Map() // 存储图片的 alt 属性，用于检测变化

let insertFn: any

const editorConfig: Partial<IEditorConfig> = {
    MENU_CONF: {
        uploadImage: {
            async customUpload(file: File, insertFn: any) {
                try {
                    console.log('开始处理图片上传...', file)
                    const formData = new FormData()
                    formData.append('file', file)
                    // 调用 insertFn 插入图片
                    const res = await UploadImages(formData)
                    console.log(res, '-res')
                    insertFn(res.uri, res.name, res.uri)

                    // 插入图片描述
                    setTimeout(() => {
                        if (editorRef.value && isInitialized) {
                            insertImageDescription(editorRef.value)
                        }
                    }, 100)
                } catch (error) {
                    console.error('图片上传失败:', error)
                }
            },
            readOnly: false,
            autoFocus: false,
            fieldName: 'file',
            maxFileSize: 10 * 1024 * 1024, // 10M
            allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'],
            pasteIgnoreImg: false
        },
        uploadVideo: {
            customBrowseAndUpload(insert: any) {
                fileType.value = 'video'
                materialPickerRef.value?.showPopup(-1)
                insertFn = insert
            }
        }
    }
}

const styles = computed<CSSProperties>(() => ({
    height: addUnit(props.height),
    width: addUnit(props.width)
}))
const valueHtml = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

// 监听 modelValue 变化，处理初始加载的图片描述
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue && isInitialized && editorRef.value) {
            // 延迟处理，确保编辑器内容已更新
            setTimeout(() => {
                console.log('检测到内容变化，处理图片描述')
                handleImageDescriptionChange(editorRef.value)
            }, 300)
        }
    },
    { immediate: false }
)

const selectChange = (fileUrl: string[]) => {
    fileUrl.forEach((url) => {
        insertFn(url)
    })
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

const handleCreated = (editor: any) => {
    editorRef.value = editor

    // 添加粘贴图片监听器
    const editorDom = editor.getEditableContainer()
    if (editorDom) {
        editorDom.addEventListener('paste', handlePasteImage)
        console.log('已添加粘贴监听器')
    }

    // 延迟初始化
    setTimeout(() => {
        isInitialized = true
        console.log('编辑器初始化完成')

        // 初始化图片数据记录
        initializeImageData(editor)

        // 处理已存在的图片描述
        setTimeout(() => {
            handleImageDescriptionChange(editor)
        }, 200)
    }, 1000)
}

// 初始化图片数据记录
const initializeImageData = (editor: any) => {
    try {
        const html = editor.getHtml()
        if (!html || !html.includes('<img')) {
            return
        }

        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = html
        const images = tempDiv.querySelectorAll('img')

        lastImageData.clear()
        images.forEach((img: HTMLImageElement, index: number) => {
            const src = img.getAttribute('src')
            const alt = img.getAttribute('alt') || ''
            const key = `${src}_${index}`
            lastImageData.set(key, alt)
        })

        console.log('初始化图片数据记录完成，共', images.length, '张图片')
    } catch (error) {
        console.error('初始化图片数据时出错:', error)
    }
}

// 处理编辑器内容变化
const handleEditorChange = (editor: any) => {
    if (!editor || !isInitialized || isProcessing) {
        return
    }

    // 检测图片 alt 属性是否发生变化
    setTimeout(() => {
        detectImageAltChanges(editor)
    }, 100)
}

// 检测图片 alt 属性变化
const detectImageAltChanges = (editor: any) => {
    try {
        const html = editor.getHtml()
        if (!html || !html.includes('<img')) {
            return
        }

        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = html
        const images = tempDiv.querySelectorAll('img')

        let hasAltChanges = false
        const currentImageData = new Map()

        images.forEach((img: HTMLImageElement, index: number) => {
            const src = img.getAttribute('src')
            const alt = img.getAttribute('alt') || ''
            const key = `${src}_${index}`

            currentImageData.set(key, alt)

            // 检查是否有变化
            if (lastImageData.has(key)) {
                const oldAlt = lastImageData.get(key)
                if (oldAlt !== alt) {
                    console.log(`检测到图片 ${index + 1} 的描述变化: "${oldAlt}" -> "${alt}"`)
                    hasAltChanges = true
                }
            } else {
                // 新图片
                lastImageData.set(key, alt)
            }
        })

        // 更新存储的图片数据
        lastImageData = currentImageData

        // 如果检测到 alt 属性变化，则处理图片描述
        if (hasAltChanges) {
            console.log('检测到图片描述变化，开始处理...')
            handleImageDescriptionChange(editor)
        }
    } catch (error) {
        console.error('检测图片变化时出错:', error)
    }
}

// 处理图片描述变化的函数（简化版本，只在上传时使用）
const handleImageDescriptionChange = (editor: any) => {
    if (!editor || isProcessing || !isInitialized) {
        console.log('跳过图片描述处理：编辑器未准备好')
        return
    }

    try {
        isProcessing = true
        const html = editor.getHtml()

        if (!html || html.trim() === '' || !html.includes('<img')) {
            isProcessing = false
            return
        }

        console.log('开始处理图片描述...')

        // 使用 DOM 操作来添加描述
        const editorDom = editor.getEditableContainer()
        if (!editorDom) {
            isProcessing = false
            return
        }

        const images = editorDom.querySelectorAll('img')
        let hasChanges = false

        images.forEach((img: HTMLImageElement, index: number) => {
            const alt = img.getAttribute('alt')

            // 检查图片后面是否已经有描述
            let nextElement = img.nextElementSibling
            while (nextElement && nextElement.nodeType === Node.TEXT_NODE) {
                nextElement = nextElement.nextElementSibling
            }

            if (alt && alt.trim()) {
                // 如果已经有描述，检查是否需要更新
                if (nextElement && nextElement.classList.contains('img-desc')) {
                    if (nextElement.textContent !== alt) {
                        console.log(`更新图片 ${index + 1} 的描述`)
                        nextElement.textContent = alt
                        hasChanges = true
                    }
                } else {
                    // 创建新的描述元素
                    console.log(`为图片 ${index + 1} 创建新描述: "${alt}"`)
                    const descDiv = document.createElement('div')
                    descDiv.className = 'img-desc'
                    descDiv.textContent = alt
                    descDiv.style.textAlign = 'center'
                    descDiv.style.color = '#333'
                    descDiv.style.fontSize = '14px'
                    descDiv.style.marginBottom = '1em'

                    // 插入到图片后面
                    img.parentNode?.insertBefore(descDiv, img.nextSibling)
                    hasChanges = true
                }
            }
        })

        if (hasChanges) {
            console.log('图片描述处理完成')
        }
    } catch (error) {
        console.error('Error handling image description change:', error)
    } finally {
        isProcessing = false
    }
}

// 插入图片描述的函数（保留用于上传时使用）
const insertImageDescription = (editor: any) => {
    console.log('调用 insertImageDescription，委托给 handleImageDescriptionChange')
    handleImageDescriptionChange(editor)
}

// 处理粘贴图片
const handlePasteImage = async (event: ClipboardEvent) => {
    console.log('粘贴事件触发', event)

    const clipboardData = event.clipboardData
    if (!clipboardData) return

    const items = clipboardData.items
    if (!items || items.length === 0) return
    // 查找图片类型
    for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.type.includes('image')) {
            console.log('检测到图片粘贴')
            event.preventDefault() // 阻止默认粘贴行为

            const file = item.getAsFile()
            if (file) {
                console.log('获取到图片文件:', file)
                const formData = new FormData()
                formData.append('file', file)
                const res = await UploadImages(formData)
                console.log(res, '-res')
                ElMessage.success('粘贴---图片上传成功')
                const editor = editorRef.value
                if (editor) {
                    editor.dangerouslyInsertHtml(
                        `<img src="${res.uri}" alt="${res.name}" style="max-width: 100%; height: auto;" />`
                    )
                    // 插入图片描述
                    setTimeout(() => {
                        if (editor && isInitialized) {
                            insertImageDescription(editor)
                        }
                    }, 100)
                }
            }
            break
        }
    }
}

// 手动处理图片描述的方法
const processImageDescriptions = () => {
    if (editorRef.value && isInitialized) {
        console.log('手动触发图片描述处理')
        handleImageDescriptionChange(editorRef.value)
    }
}

// 组件挂载后检查图片描述
onMounted(() => {
    // 延迟检查，确保编辑器完全初始化
    setTimeout(() => {
        if (
            props.modelValue &&
            props.modelValue.includes('<img') &&
            editorRef.value &&
            isInitialized
        ) {
            console.log('组件挂载后检查图片描述')
            handleImageDescriptionChange(editorRef.value)
        }
    }, 1500)
})

// 暴露方法给父组件
defineExpose({
    processImageDescriptions
})
</script>

<style lang="scss">
.w-e-full-screen-container {
    z-index: 999;
}
.w-e-text-container [data-slate-editor] ul {
    list-style: disc;
}
.w-e-text-container [data-slate-editor] ol {
    list-style: decimal;
}
h1 {
    font-size: 2em;
}
h2 {
    font-size: 1.5em;
}
h3 {
    font-size: 1.17em;
}
h4 {
    font-size: 1em;
}
h5 {
    font-size: 0.83em;
}
h1,
h2,
h3,
h4,
h5 {
    font-weight: bold;
}
.img-desc {
    text-align: center;
    color: #888;
    font-size: 13px;
    margin-bottom: 1em;
}
</style>
