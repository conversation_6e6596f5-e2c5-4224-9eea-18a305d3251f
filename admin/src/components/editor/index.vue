<template>
    <div class="border border-br flex flex-col" :style="styles">
        <toolbar
            class="border-b border-br"
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            :mode="mode"
        />
        <w-editor
            class="flex-1 overflow-hidden"
            v-model="valueHtml"
            :defaultConfig="editorConfig"
            :mode="mode"
            @onCreated="handleCreated"
            @onChange="handleEditorChange"
        />
        <material-picker
            ref="materialPickerRef"
            :type="fileType"
            :limit="-1"
            hidden-upload
            @change="selectChange"
        />
    </div>
</template>
<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import { addUnit } from '@/utils/util'
import { uploadImage as UploadImages } from '@/api/app/recharge.ts'
import { Editor as WEditor, Toolbar } from '@wangeditor/editor-for-vue'
import MaterialPicker from '@/components/material/picker.vue'
import type { CSSProperties } from 'vue'
import type { IEditorConfig, IToolbarConfig } from '@wangeditor/editor'

const props = withDefaults(
    defineProps<{
        modelValue?: string
        mode?: 'default' | 'simple'
        height?: string | number
        width?: string | number
        toolbarConfig?: Partial<IToolbarConfig>
    }>(),
    {
        modelValue: '',
        mode: 'default',
        height: '100%',
        width: 'auto',
        toolbarConfig: () => ({})
    }
)

const emit = defineEmits<{
    (event: 'update:modelValue', value: string): void
}>()

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const materialPickerRef = shallowRef<InstanceType<typeof MaterialPicker>>()
const fileType = ref('')
let isProcessing = false // 添加处理标记
let isInitialized = false // 添加初始化标记
let lastImageData = new Map() // 存储图片的 alt 属性，用于检测变化
let uploadedImages = new Set() // 记录刚上传的图片，这些图片不显示描述
let isUpdatingFromInternal = false // 标记是否是内部更新，避免循环

let insertFn: any

const editorConfig: Partial<IEditorConfig> = {
    MENU_CONF: {
        uploadImage: {
            async customUpload(file: File, insertFn: any) {
                try {
                    console.log('开始处理图片上传...', file)
                    const formData = new FormData()
                    formData.append('file', file)
                    // 调用 insertFn 插入图片
                    const res = await UploadImages(formData)
                    console.log(res, '-res')
                    insertFn(res.uri, res.name, res.uri)

                    // 记录刚上传的图片，不显示描述
                    uploadedImages.add(res.uri)
                    console.log('图片上传完成，已记录为新上传图片:', res.uri)

                    // 不再自动插入图片描述
                    // 只有用户手动修改过描述后才显示
                } catch (error) {
                    console.error('图片上传失败:', error)
                }
            },
            readOnly: false,
            autoFocus: false,
            fieldName: 'file',
            maxFileSize: 10 * 1024 * 1024, // 10M
            allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'],
            pasteIgnoreImg: false
        },
        uploadVideo: {
            customBrowseAndUpload(insert: any) {
                fileType.value = 'video'
                materialPickerRef.value?.showPopup(-1)
                insertFn = insert
            }
        }
    }
}

const styles = computed<CSSProperties>(() => ({
    height: addUnit(props.height),
    width: addUnit(props.width)
}))
const valueHtml = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)

        // 当内容被设置时，立即检查图片描述（但避免内部更新时的循环）
        if (
            value &&
            value.includes('<img') &&
            editorRef.value &&
            isInitialized &&
            !isUpdatingFromInternal
        ) {
            nextTick(() => {
                handleImageDescriptionChange(editorRef.value)
            })
        }
    }
})

// 监听 modelValue 变化，处理初始加载的图片描述
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue && newValue.includes('<img') && editorRef.value) {
            // 减少延迟，更快响应
            setTimeout(() => {
                console.log('检测到内容变化，处理图片描述')
                if (isInitialized) {
                    handleImageDescriptionChange(editorRef.value)
                } else {
                    // 如果编辑器还未初始化，等待初始化完成
                    const checkInit = setInterval(() => {
                        if (isInitialized && editorRef.value) {
                            clearInterval(checkInit)
                            handleImageDescriptionChange(editorRef.value)
                        }
                    }, 50)

                    // 最多等待 3 秒
                    setTimeout(() => clearInterval(checkInit), 3000)
                }
            }, 100)
        }
    },
    { immediate: true }
)

const selectChange = (fileUrl: string[]) => {
    fileUrl.forEach((url) => {
        insertFn(url)
    })
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

const handleCreated = (editor: any) => {
    editorRef.value = editor

    // 添加粘贴图片监听器
    const editorDom = editor.getEditableContainer()
    if (editorDom) {
        editorDom.addEventListener('paste', handlePasteImage)
        console.log('已添加粘贴监听器')
    }

    // 延迟初始化
    setTimeout(() => {
        isInitialized = true
        console.log('编辑器初始化完成')

        // 初始化图片数据记录
        initializeImageData(editor)

        // 立即处理已存在的图片描述
        handleImageDescriptionChange(editor)
    }, 500)
}

// 初始化图片数据记录
const initializeImageData = (editor: any) => {
    try {
        const html = editor.getHtml()
        if (!html || !html.includes('<img')) {
            return
        }

        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = html
        const images = tempDiv.querySelectorAll('img')

        lastImageData.clear()
        images.forEach((img: HTMLImageElement, index: number) => {
            const src = img.getAttribute('src')
            const alt = img.getAttribute('alt') || ''
            const key = `${src}_${index}`
            lastImageData.set(key, alt)
        })

        console.log('初始化图片数据记录完成，共', images.length, '张图片')
    } catch (error) {
        console.error('初始化图片数据时出错:', error)
    }
}

// 处理编辑器内容变化
const handleEditorChange = (editor: any) => {
    if (!editor || !isInitialized || isProcessing) {
        return
    }

    // 检测图片 alt 属性是否发生变化
    setTimeout(() => {
        detectImageAltChanges(editor)
    }, 100)
}

// 检测图片 alt 属性变化
const detectImageAltChanges = (editor: any) => {
    try {
        const html = editor.getHtml()
        if (!html || !html.includes('<img')) {
            return
        }

        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = html
        const images = tempDiv.querySelectorAll('img')

        let hasAltChanges = false
        const currentImageData = new Map()

        images.forEach((img: HTMLImageElement, index: number) => {
            const src = img.getAttribute('src')
            const alt = img.getAttribute('alt') || ''
            const key = `${src}_${index}`

            currentImageData.set(key, alt)

            // 检查是否有变化
            if (lastImageData.has(key)) {
                const oldAlt = lastImageData.get(key)
                if (oldAlt !== alt) {
                    console.log(`检测到图片 ${index + 1} 的描述变化: "${oldAlt}" -> "${alt}"`)
                    hasAltChanges = true

                    // 如果用户手动修改了描述，将该图片从"刚上传"列表中移除
                    if (uploadedImages.has(src)) {
                        uploadedImages.delete(src)
                        console.log(`图片 ${src} 已被用户修改描述，移出新上传列表`)
                    }
                }
            } else {
                // 新图片
                lastImageData.set(key, alt)
            }
        })

        // 更新存储的图片数据
        lastImageData = currentImageData

        // 如果检测到 alt 属性变化，则处理图片描述
        if (hasAltChanges) {
            console.log('检测到图片描述变化，开始处理...')
            handleImageDescriptionChange(editor)
        }
    } catch (error) {
        console.error('检测图片变化时出错:', error)
    }
}

// 处理图片描述变化的函数（简化版本，只在上传时使用）
const handleImageDescriptionChange = (editor: any) => {
    if (!editor || isProcessing || !isInitialized) {
        console.log('跳过图片描述处理：编辑器未准备好')
        return
    }

    try {
        isProcessing = true
        const html = editor.getHtml()

        if (!html || html.trim() === '' || !html.includes('<img')) {
            isProcessing = false
            return
        }

        console.log('开始处理图片描述...')

        // 使用 DOM 操作来添加描述
        const editorDom = editor.getEditableContainer()
        if (!editorDom) {
            isProcessing = false
            return
        }

        const images = editorDom.querySelectorAll('img')
        let hasChanges = false

        images.forEach((img: HTMLImageElement, index: number) => {
            const alt = img.getAttribute('alt')
            const src = img.getAttribute('src')

            // 检查图片后面是否已经有描述
            let nextElement = img.nextElementSibling
            while (nextElement && nextElement.nodeType === Node.TEXT_NODE) {
                nextElement = nextElement.nextElementSibling
            }

            if (alt && alt.trim()) {
                // 检查是否是刚上传的图片
                if (uploadedImages.has(src)) {
                    console.log(`图片 ${index + 1} 是刚上传的，跳过描述显示`)
                    return
                }

                // 如果已经有描述，检查是否需要更新
                if (nextElement && nextElement.classList.contains('img-desc')) {
                    if (nextElement.textContent !== alt) {
                        console.log(`更新图片 ${index + 1} 的描述`)
                        nextElement.textContent = alt
                        console.log('🚀 ~ images.forEach ~ nextElement:', nextElement)
                        hasChanges = true
                    }
                } else {
                    // 创建新的描述元素
                    console.log(`为图片 ${index + 1} 创建新描述: "${alt}"`)
                    const descDiv = document.createElement('div')
                    descDiv.className = 'img-desc'
                    descDiv.textContent = alt
                    descDiv.style.textAlign = 'center'
                    descDiv.style.color = '#333'
                    descDiv.style.fontSize = '14px'
                    descDiv.style.marginBottom = '1em'

                    // 插入到图片后面
                    img.parentNode?.insertBefore(descDiv, img.nextSibling)
                    hasChanges = true
                }
            }
        })

        if (hasChanges) {
            console.log('图片描述处理完成，更新 valueHtml')

            // 设置内部更新标记，避免循环
            isUpdatingFromInternal = true

            // 获取更新后的 HTML 内容
            const updatedHtml = editor.getHtml()

            // 更新 valueHtml，触发父组件的 modelValue 更新
            emit('update:modelValue', updatedHtml)

            // 重置内部更新标记
            setTimeout(() => {
                isUpdatingFromInternal = false
            }, 100)
        }
    } catch (error) {
        console.error('Error handling image description change:', error)
    } finally {
        isProcessing = false
    }
}

// 处理粘贴图片
const handlePasteImage = async (event: ClipboardEvent) => {
    console.log('粘贴事件触发', event)

    const clipboardData = event.clipboardData
    if (!clipboardData) return

    const items = clipboardData.items
    if (!items || items.length === 0) return
    // 查找图片类型
    for (let i = 0; i < items.length; i++) {
        const item = items[i]
        if (item.type.includes('image')) {
            console.log('检测到图片粘贴')
            event.preventDefault() // 阻止默认粘贴行为

            const file = item.getAsFile()
            if (file) {
                console.log('获取到图片文件:', file)
                const formData = new FormData()
                formData.append('file', file)
                const res = await UploadImages(formData)
                console.log(res, '-res')
                ElMessage.success('粘贴---图片上传成功')
                const editor = editorRef.value
                if (editor) {
                    editor.dangerouslyInsertHtml(
                        `<img src="${res.uri}" alt="${res.name}" style="max-width: 100%; height: auto;" />`
                    )
                    // 记录刚粘贴的图片，不显示描述
                    uploadedImages.add(res.uri)
                    console.log('图片粘贴完成，已记录为新上传图片:', res.uri)
                }
            }
            break
        }
    }
}

// 手动处理图片描述的方法
const processImageDescriptions = () => {
    if (editorRef.value && isInitialized) {
        console.log('手动触发图片描述处理')
        handleImageDescriptionChange(editorRef.value)
    }
}

// 组件挂载后检查图片描述
onMounted(() => {
    // 如果有初始内容，立即尝试处理
    if (props.modelValue && props.modelValue.includes('<img')) {
        console.log('组件挂载，检测到图片内容')

        // 等待编辑器初始化的轮询检查
        const checkAndProcess = () => {
            if (editorRef.value && isInitialized) {
                console.log('编辑器已准备好，处理图片描述')
                handleImageDescriptionChange(editorRef.value)
                return true
            }
            return false
        }

        // 立即尝试一次
        if (!checkAndProcess()) {
            // 如果失败，开始轮询
            const pollInterval = setInterval(() => {
                if (checkAndProcess()) {
                    clearInterval(pollInterval)
                }
            }, 100)

            // 最多轮询 5 秒
            setTimeout(() => clearInterval(pollInterval), 5000)
        }
    }
})

// 暴露方法给父组件
defineExpose({
    processImageDescriptions
})
</script>

<style lang="scss">
.w-e-full-screen-container {
    z-index: 999;
}
.w-e-text-container [data-slate-editor] ul {
    list-style: disc;
}
.w-e-text-container [data-slate-editor] ol {
    list-style: decimal;
}
h1 {
    font-size: 2em;
}
h2 {
    font-size: 1.5em;
}
h3 {
    font-size: 1.17em;
}
h4 {
    font-size: 1em;
}
h5 {
    font-size: 0.83em;
}
h1,
h2,
h3,
h4,
h5 {
    font-weight: bold;
}
.img-desc {
    text-align: center;
    color: #888;
    font-size: 13px;
    margin-bottom: 1em;
}
</style>
