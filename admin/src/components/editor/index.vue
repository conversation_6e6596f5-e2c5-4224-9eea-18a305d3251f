<template>
    <div class="border border-br flex flex-col" :style="styles">
        <toolbar
            class="border-b border-br"
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            :mode="mode"
        />
        <w-editor
            class="flex-1 overflow-hidden"
            v-model="valueHtml"
            :defaultConfig="editorConfig"
            :mode="mode"
            @onCreated="handleCreated"
        />
        <material-picker
            ref="materialPickerRef"
            :type="fileType"
            :limit="-1"
            hidden-upload
            @change="selectChange"
        />
    </div>
</template>
<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import type { IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { Editor as WEditor, Toolbar } from '@wangeditor/editor-for-vue'
import type { CSSProperties } from 'vue'
import MaterialPicker from '@/components/material/picker.vue'
import { addUnit } from '@/utils/util'
import { uploadImage as UploadImages } from '@/api/app/recharge.ts'

const props = withDefaults(
    defineProps<{
        modelValue?: string
        mode?: 'default' | 'simple'
        height?: string | number
        width?: string | number
        toolbarConfig?: Partial<IToolbarConfig>
    }>(),
    {
        modelValue: '',
        mode: 'default',
        height: '100%',
        width: 'auto',
        toolbarConfig: () => ({})
    }
)

const emit = defineEmits<{
    (event: 'update:modelValue', value: string): void
}>()

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()
const materialPickerRef = shallowRef<InstanceType<typeof MaterialPicker>>()
const fileType = ref('')
let isProcessing = false // 添加处理标记
let isInitialized = false // 添加初始化标记
let lastHtml = '' // 移到外部

let insertFn: any

const editorConfig: Partial<IEditorConfig> = {
    MENU_CONF: {

        uploadImage: {
            async customUpload(file: File, insertFn: any){
                try {
                    console.log('开始处理图片上传...',file);
                    const formData = new FormData();
                    formData.append('file', file);
                    // 调用 insertFn 插入图片
                    const res = await UploadImages(formData)
                    console.log(res,'-res')
                    insertFn(res.uri, res.name, res.uri);
                    
                    // 插入图片描述
                    setTimeout(() => {
                        if (editorRef.value && isInitialized) {
                            insertImageDescription(editorRef.value);
                        }
                    }, 100);


                } catch (error) {
                    console.error('图片上传失败:', error);
                }
            },
            readOnly: false,
            autoFocus: false,
            fieldName: 'file',
            maxFileSize: 10 * 1024 * 1024, // 10M
            allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'],
            pasteIgnoreImg: false,
        },
        uploadVideo: {
            customBrowseAndUpload(insert: any) {
                fileType.value = 'video'
                materialPickerRef.value?.showPopup(-1)
                insertFn = insert
            }
        }
    }
}

const styles = computed<CSSProperties>(() => ({
    height: addUnit(props.height),
    width: addUnit(props.width)
}))
const valueHtml = computed({
    get() {
        return props.modelValue
    },
    set(value) {
        emit('update:modelValue', value)
    }
})

const selectChange = (fileUrl: string[]) => {
    fileUrl.forEach((url) => {
        insertFn(url)
    })
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
    const editor = editorRef.value
    if (editor == null) return
    editor.destroy()
})

const handleCreated = (editor: any) => {
    editorRef.value = editor;
    
    // 添加粘贴图片监听器
    const editorDom = editor.getEditableContainer();
    if (editorDom) {
        editorDom.addEventListener("paste", handlePasteImage);
        console.log("已添加粘贴监听器");
    }
    
    // 延迟初始化
    setTimeout(() => {
        isInitialized = true;
        console.log('编辑器初始化完成');
    }, 1000);
}

// 插入图片描述的函数
const insertImageDescription = (editor: any) => {
    if (!editor || isProcessing || !isInitialized) {
        console.log('跳过图片描述处理：编辑器未准备好');
        return;
    }
    
    try {
        isProcessing = true;
        const html = editor.getHtml();
        
        // 如果内容为空，不处理
        if (!html || html.trim() === '') {
            console.log('内容为空，跳过处理');
            return;
        }
        
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        let hasChanges = false;
        const images = tempDiv.querySelectorAll('img');
        console.log(`找到 ${images.length} 张图片，开始处理描述...`);
        
        images.forEach((img, index) => {
            const alt = img.getAttribute('alt');
            const nextSibling = img.nextElementSibling;
            
            console.log(`图片 ${index + 1}: alt="${alt}"`);
            
            if (alt && alt.trim()) {
                // 如果图片后面已经有描述，检查是否需要更新
                if (nextSibling && nextSibling.classList.contains('img-desc')) {
                    if (nextSibling.textContent !== alt) {
                        // 更新现有描述
                        console.log(`更新图片 ${index + 1} 的描述: "${nextSibling.textContent}" -> "${alt}"`);
                        nextSibling.textContent = alt;
                        hasChanges = true;
                    }
                } else {
                    // 创建新的描述元素
                    console.log(`为图片 ${index + 1} 创建新描述: "${alt}"`);
                    const descDiv = document.createElement('div');
                    descDiv.className = 'img-desc';
                    descDiv.textContent = alt;
                    descDiv.style.textAlign = 'center';
                    descDiv.style.color = '#888';
                    descDiv.style.fontSize = '13px';
                    descDiv.style.marginBottom = '1em';
                    
                    // 插入到图片后面
                    img.parentNode?.insertBefore(descDiv, img.nextSibling);
                    hasChanges = true;
                }
            }
        });
        
        // 只有在有变化时才更新编辑器内容
        if (hasChanges) {
            console.log('检测到变化，更新编辑器内容');
            const newHtml = tempDiv.innerHTML;
            editor.setHtml(newHtml);
        } else {
            console.log('没有检测到变化');
        }
    } catch (error) {
        console.error('Error inserting image description:', error);
    } finally {
        isProcessing = false;
    }
}


// 处理粘贴图片
const handlePasteImage = async (event: ClipboardEvent) => {
    console.log("粘贴事件触发", event);

    const clipboardData = event.clipboardData;
    if (!clipboardData) return;

    const items = clipboardData.items;
    if (!items || items.length === 0) return;
    // 查找图片类型
    for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.includes("image")) {
            console.log("检测到图片粘贴");
            event.preventDefault(); // 阻止默认粘贴行为

            const file = item.getAsFile();
            if (file) {
                console.log("获取到图片文件:", file);
                const formData = new FormData();
                formData.append('file', file);
                const res = await UploadImages(formData)
                console.log(res,'-res')
                ElMessage.success("粘贴---图片上传成功");
                const editor = editorRef.value;
                    if (editor) {
                        editor.dangerouslyInsertHtml(
                            `<img src="${res.uri}" alt="${res.name}" style="max-width: 100%; height: auto;" />`
                        );
                        // 插入图片描述
                        setTimeout(() => {
                            if (editor && isInitialized) {
                                insertImageDescription(editor);
                            }
                        }, 100);
                    }
            }
            break;
        }
    }
};

// 手动处理图片描述的方法
const processImageDescriptions = () => {
    if (editorRef.value && isInitialized) {
        insertImageDescription(editorRef.value);
    }
}

// 暴露方法给父组件
defineExpose({
    processImageDescriptions
})
</script>

<style lang="scss">
.w-e-full-screen-container {
    z-index: 999;
}
.w-e-text-container [data-slate-editor] ul {
    list-style: disc;
}
.w-e-text-container [data-slate-editor] ol {
    list-style: decimal;
}
h1 {
    font-size: 2em;
}
h2 {
    font-size: 1.5em;
}
h3 {
    font-size: 1.17em;
}
h4 {
    font-size: 1em;
}
h5 {
    font-size: 0.83em;
}
h1,
h2,
h3,
h4,
h5 {
    font-weight: bold;
}
.img-desc {
    text-align: center;
    color: #888;
    font-size: 13px;
    margin-bottom: 1em;
}
</style>
