{"name": "vue-project", "version": "0.0.0", "license": "MIT", "scripts": {"dev": "vite", "preview": "vite preview --port 4173", "build": "vite build && node scripts/release.mjs", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@highlightjs/vue-plugin": "^2.1.0", "@vue/shared": "^3.5.13", "@vueuse/core": "^12.7.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.9", "css-color-function": "^1.3.3", "echarts": "^5.6.0", "element-plus": "^2.10.2", "highlight.js": "^11.11.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.3.1", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-echarts": "^6.7.3", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.5", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.4", "@types/nprogress": "^0.2.3", "@vitejs/plugin-legacy": "^6.0.1", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.4.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "consola": "^3.4.0", "eslint": "^9.10.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^9.32.0", "execa": "^9.5.2", "fs-extra": "^11.3.0", "postcss": "^8.5.3", "prettier": "^3.5.1", "sass": "1.79.6", "tailwindcss": "^3.4.17", "terser": "^5.39.0", "typescript": "^5.7.3", "unplugin-auto-import": "^19.1.0", "unplugin-vue-components": "^28.4.0", "vite": "^6.1.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^2.2.2"}}